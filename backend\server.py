import os
import asyncio
import uuid
import time
import json
import csv
import aiohttp
import aiofiles
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Optional, Any, Union
from enum import Enum
from pathlib import Path

from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException, UploadFile, File
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, FileResponse
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel, Field
from dotenv import load_dotenv

# Load environment variables
# Try to load from multiple locations
env_paths = [
    '.env',  # Current directory
    os.path.join(os.path.dirname(__file__), '.env'),  # Backend directory
    os.path.join(os.path.dirname(__file__), '..', '.env')  # Parent directory
]

for env_path in env_paths:
    if os.path.exists(env_path):
        load_dotenv(env_path)
        print(f"Loaded environment from: {env_path}")
        break
else:
    print("Warning: No .env file found. Using default values.")

# Configuration
DATA_DIR = os.path.join(os.path.dirname(__file__), 'data')
TASKS_FILE = os.path.join(DATA_DIR, 'tasks.json')
MAX_CONCURRENT_UPLOADS = int(os.environ.get('MAX_CONCURRENT_UPLOADS', '5'))
MAX_QUEUE_SIZE = int(os.environ.get('MAX_QUEUE_SIZE', '50'))
UPLOAD_TIMEOUT_MINUTES = int(os.environ.get('UPLOAD_TIMEOUT_MINUTES', '30'))

# Platform API Keys
PLATFORM_API_KEYS = {
    'lulustream': os.environ.get('LULUSTREAM_API_KEY', ''),
    'streamp2p': os.environ.get('STREAMP2P_API_KEY', ''),
    'rpmshare': os.environ.get('RPMSHARE_API_KEY', ''),
    'filemoon': os.environ.get('FILEMOON_API_KEY', ''),
    'upnshare': os.environ.get('UPNSHARE_API_KEY', '')
}

# Debug: Check if API keys are loaded
print("API Keys Status:")
for platform, key in PLATFORM_API_KEYS.items():
    status = "[OK] Loaded" if key else "[MISSING]"
    print(f"  {platform}: {status}")
print()

# Enums
class Platform(str, Enum):
    LULUSTREAM = "lulustream"
    STREAMP2P = "streamp2p"
    RPMSHARE = "rpmshare"
    FILEMOON = "filemoon"
    UPNSHARE = "upnshare"

class UploadStatus(str, Enum):
    QUEUED = "queued"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class UploadType(str, Enum):
    REMOTE_URL = "remote_url"

# Platform Configurations based on API documentation
PLATFORM_CONFIGS = {
    Platform.LULUSTREAM: {
        "api_key": PLATFORM_API_KEYS['lulustream'],
        "base_url": "https://lulustream.com/api",
        "upload_endpoint": "/upload/url",
        "status_endpoint": "/file/url_uploads",
        "info_endpoint": "/file/info",
        "embed_template": '<iframe src="https://luluvid.com/e/{file_code}" scrolling="no" frameborder="0" width="640" height="360" allowfullscreen="true" webkitallowfullscreen="true" mozallowfullscreen="true"></iframe>',
        "method": "GET"
    },
    Platform.STREAMP2P: {
        "api_key": PLATFORM_API_KEYS['streamp2p'],
        "base_url": "https://streamp2p.com/api/v1",
        "upload_endpoint": "/video/advance-upload",
        "status_endpoint": "/video/advance-upload",
        "info_endpoint": "/video",
        "embed_template": '<iframe src="https://streamdb.p2pstream.online/#{file_code}" width="100%" height="100%" frameborder="0" allowfullscreen></iframe>',
        "method": "POST",
        "headers": {"api-token": PLATFORM_API_KEYS['streamp2p'], "Content-Type": "application/json"}
    },
    Platform.RPMSHARE: {
        "api_key": PLATFORM_API_KEYS['rpmshare'],
        "base_url": "https://rpmshare.com/api/v1",
        "upload_endpoint": "/video/advance-upload",
        "status_endpoint": "/video/advance-upload",
        "info_endpoint": "/video",
        "embed_template": '<iframe src="https://streamdb.rpmstream.online/#{file_code}" width="100%" height="100%" frameborder="0" allowfullscreen></iframe>',
        "method": "POST",
        "headers": {"api-token": PLATFORM_API_KEYS['rpmshare'], "Content-Type": "application/json"}
    },
    Platform.FILEMOON: {
        "api_key": PLATFORM_API_KEYS['filemoon'],
        "base_url": "https://filemoonapi.com/api",
        "upload_endpoint": "/remote/add",
        "status_endpoint": "/remote/status",
        "info_endpoint": "/file/info",
        "list_endpoint": "/file/list",
        "embed_template": '<iframe src="https://filemoon.to/e/{file_code}/{filename}" frameborder="0" marginwidth="0" marginheight="0" scrolling="no" width="640" height="360" allowfullscreen></iframe>',
        "method": "GET"
    },
    Platform.UPNSHARE: {
        "api_key": PLATFORM_API_KEYS['upnshare'],
        "base_url": "https://upnshare.com/api/v1",
        "upload_endpoint": "/video/advance-upload",
        "status_endpoint": "/video/advance-upload",
        "info_endpoint": "/video",
        "embed_template": '<iframe src="https://streamdb.upns.online/#{file_code}" width="100%" height="100%" frameborder="0" allowfullscreen></iframe>',
        "method": "POST",
        "headers": {"api-token": PLATFORM_API_KEYS['upnshare'], "Content-Type": "application/json"},
        "rate_limit_delay": 10  # Add delay between requests to handle rate limiting
    }
}

# Pydantic Models
class UploadTask(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    url: str
    filename: str
    upload_type: UploadType = UploadType.REMOTE_URL
    status: UploadStatus = UploadStatus.QUEUED
    session_id: str

    # Progress tracking
    progress: Dict[str, float] = Field(default_factory=dict)  # Platform -> percentage
    speeds: Dict[str, float] = Field(default_factory=dict)    # Platform -> MB/s
    eta_seconds: Dict[str, int] = Field(default_factory=dict) # Platform -> seconds
    embed_codes: Dict[str, str] = Field(default_factory=dict) # Platform -> embed code
    file_codes: Dict[str, str] = Field(default_factory=dict)  # Platform -> file code

    # Timestamps
    created_at: datetime = Field(default_factory=datetime.utcnow)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    last_updated: Optional[datetime] = None

    # Status tracking
    platform_status: Dict[str, str] = Field(default_factory=dict)
    error_messages: Dict[str, str] = Field(default_factory=dict)

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class UrlSubmissionRequest(BaseModel):
    urls: List[str]
    session_id: Optional[str] = None

class QueueStatusResponse(BaseModel):
    total_queued: int
    total_in_progress: int
    total_completed: int
    total_failed: int
    current_queue: List[UploadTask]
    active_uploads: List[UploadTask]
    completed_uploads: List[UploadTask]

# Global state
upload_queue: List[UploadTask] = []
active_uploads: Dict[str, UploadTask] = {}
completed_uploads: Dict[str, UploadTask] = {}
failed_uploads: Dict[str, UploadTask] = {}
cancel_flags: Dict[str, asyncio.Event] = {}
running_platform_tasks: Dict[str, List[asyncio.Task]] = {}

# Platform-specific sequential upload management for Lulustream & UpnShare
lulustream_active_uploads = 0
upnshare_active_uploads = 0
lulustream_queue: List[Dict] = []  # Queue of {task_id, task} for sequential processing
upnshare_queue: List[Dict] = []   # Queue of {task_id, task} for sequential processing
MAX_SEQUENTIAL_UPLOADS = 1  # Only 1 upload at a time for these platforms

# Rate limiting state
platform_rate_limits: Dict[str, Dict[str, Any]] = {}
rate_limited_tasks: Dict[str, List[Dict[str, Any]]] = {}  # platform -> list of pending retry tasks
retry_attempts: Dict[str, Dict[str, int]] = {}  # task_id -> {platform: attempt_count}

# CSV monitoring disabled - no auto CSV generation

# WebSocket connections
active_connections: List[WebSocket] = []

# Rate limiting helper functions
async def check_rate_limit(platform: str) -> Dict[str, Any]:
    """Check if platform is rate limited and return wait time"""
    if platform not in platform_rate_limits:
        return {"is_limited": False, "wait_time": 0}

    rate_limit_info = platform_rate_limits[platform]
    current_time = time.time()

    if current_time < rate_limit_info.get("retry_after", 0):
        wait_time = rate_limit_info["retry_after"] - current_time
        return {
            "is_limited": True,
            "wait_time": wait_time,
            "retry_after": rate_limit_info["retry_after"],
            "reason": rate_limit_info.get("reason", "Rate limited")
        }

    return {"is_limited": False, "wait_time": 0}

async def set_rate_limit(platform: str, wait_seconds: int, reason: str = "Rate limited"):
    """Set rate limit for a platform"""
    retry_after = time.time() + wait_seconds
    platform_rate_limits[platform] = {
        "retry_after": retry_after,
        "reason": reason,
        "set_at": time.time()
    }
    print(f"[RATE-LIMIT] {platform} rate limited for {wait_seconds}s: {reason}")

async def clear_rate_limit(platform: str):
    """Clear rate limit for a platform and trigger retry of pending tasks"""
    if platform in platform_rate_limits:
        del platform_rate_limits[platform]
        print(f"[RATE-LIMIT] {platform} rate limit cleared")

        # Trigger retry of pending tasks for this platform
        await retry_rate_limited_tasks(platform)

async def handle_http_error(platform: str, status_code: int, response_text: str = "") -> bool:
    """Handle HTTP errors and set rate limits if needed. Returns True if rate limited."""
    if status_code == 429:
        # Rate limiting
        await set_rate_limit(platform, 300, f"HTTP 429 Rate Limited")
        return True
    elif status_code == 503:
        # Service unavailable
        await set_rate_limit(platform, 120, f"HTTP 503 Service Unavailable")
        return True
    elif status_code >= 500:
        # Server errors - short backoff
        await set_rate_limit(platform, 60, f"HTTP {status_code} Server Error")
        return True

    return False

async def add_rate_limited_task(platform: str, task_id: str, task_data: Dict[str, Any]):
    """Add a task to the rate-limited retry queue (single retry per platform per task)"""
    # Initialize retry attempts tracking
    if task_id not in retry_attempts:
        retry_attempts[task_id] = {}

    # Check if this platform has already been retried for this task
    if retry_attempts[task_id].get(platform, 0) >= 1:
        print(f"[RATE-LIMIT-RETRY] Task {task_id[:8]} already retried for {platform}, skipping")
        return

    if platform not in rate_limited_tasks:
        rate_limited_tasks[platform] = []

    # Check if task already exists in retry queue
    existing_task = next((t for t in rate_limited_tasks[platform] if t["task_id"] == task_id), None)
    if not existing_task:
        retry_task = {
            "task_id": task_id,
            "platform": platform,
            "added_at": time.time(),
            "retry_attempt": retry_attempts[task_id].get(platform, 0) + 1,
            **task_data
        }
        rate_limited_tasks[platform].append(retry_task)
        print(f"[RATE-LIMIT-RETRY] Added {platform} task {task_id[:8]} to retry queue (attempt #{retry_task['retry_attempt']})")

async def retry_rate_limited_tasks(platform: str):
    """Retry all pending tasks for a platform after rate limit expires"""
    if platform not in rate_limited_tasks or not rate_limited_tasks[platform]:
        return

    pending_tasks = rate_limited_tasks[platform].copy()
    rate_limited_tasks[platform] = []  # Clear the queue

    print(f"[RATE-LIMIT-RETRY] Retrying {len(pending_tasks)} tasks for {platform}")

    for retry_task in pending_tasks:
        task_id = retry_task["task_id"]

        # Track retry attempt
        if task_id not in retry_attempts:
            retry_attempts[task_id] = {}
        retry_attempts[task_id][platform] = retry_attempts[task_id].get(platform, 0) + 1

        # Find the task in active uploads or completed uploads
        task = None
        if task_id in active_uploads:
            task = active_uploads[task_id]
        elif task_id in completed_uploads:
            task = completed_uploads[task_id]
            # Move back to active for retry
            active_uploads[task_id] = task
            del completed_uploads[task_id]
        elif task_id in failed_uploads:
            task = failed_uploads[task_id]
            # Move back to active for retry
            active_uploads[task_id] = task
            del failed_uploads[task_id]

        if task:
            # Reset platform status for retry
            task.platform_status[platform] = "retrying"
            if platform in task.error_messages:
                del task.error_messages[platform]

            # Create new platform upload task
            platform_enum = Platform(platform)
            cancel_event = cancel_flags.get(task_id, asyncio.Event())
            if task_id not in cancel_flags:
                cancel_flags[task_id] = cancel_event

            # Start the retry upload with special retry handling
            retry_upload_task = asyncio.create_task(
                retry_platform_upload(platform_enum, task, cancel_event, retry_task["retry_attempt"])
            )

            # Add to running tasks
            if task_id not in running_platform_tasks:
                running_platform_tasks[task_id] = []
            running_platform_tasks[task_id].append(retry_upload_task)

            print(f"[RATE-LIMIT-RETRY] Started retry #{retry_task['retry_attempt']} for {platform} task {task_id[:8]}")

            # Broadcast update to frontend
            await broadcast_task_update(task)

async def check_and_retry_expired_rate_limits():
    """Background task to check for expired rate limits and retry tasks"""
    while True:
        try:
            current_time = time.time()
            expired_platforms = []

            for platform, rate_info in platform_rate_limits.items():
                if current_time >= rate_info.get("retry_after", 0):
                    expired_platforms.append(platform)

            # Clear expired rate limits and retry tasks
            for platform in expired_platforms:
                await clear_rate_limit(platform)

            # Sleep for 30 seconds before next check
            await asyncio.sleep(30)

        except Exception as e:
            print(f"[RATE-LIMIT-RETRY] Error in background retry checker: {e}")
            await asyncio.sleep(60)  # Wait longer on error

async def retry_platform_upload(platform: Platform, task: UploadTask, cancel_event: asyncio.Event, retry_attempt: int):
    """Retry upload for a specific platform with final failure handling"""
    try:
        print(f"[RETRY] Starting retry #{retry_attempt} for {platform.value} - {task.filename}")

        # Call the regular upload function
        result = await upload_to_platform(platform, task, cancel_event)

        # Check if retry failed
        if result and result.get("error"):
            # This was the final retry attempt, mark as permanently failed
            task.platform_status[platform.value] = "failed"
            task.error_messages[platform.value] = f"Retry #{retry_attempt} failed: {result['error']}"

            print(f"[RETRY-FAILED] Final retry failed for {platform.value} - {task.filename}: {result['error']}")

            # Remove from retry queue if still there
            if platform.value in rate_limited_tasks:
                rate_limited_tasks[platform.value] = [
                    t for t in rate_limited_tasks[platform.value]
                    if t["task_id"] != task.id
                ]

            # Broadcast final failure
            await broadcast_task_update(task)

        return result

    except Exception as e:
        # Handle retry exception
        task.platform_status[platform.value] = "failed"
        task.error_messages[platform.value] = f"Retry #{retry_attempt} exception: {str(e)}"

        print(f"[RETRY-ERROR] Retry exception for {platform.value} - {task.filename}: {e}")

        # Remove from retry queue
        if platform.value in rate_limited_tasks:
            rate_limited_tasks[platform.value] = [
                t for t in rate_limited_tasks[platform.value]
                if t["task_id"] != task.id
            ]

        await broadcast_task_update(task)
        return {"error": f"Retry exception: {str(e)}"}

# FastAPI Lifespan Events
from contextlib import asynccontextmanager

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Handle application lifespan events"""
    # Startup
    await init_file_storage()

    # Create audio directories
    audio_dir = os.path.join(os.path.dirname(__file__), 'audio')
    os.makedirs(audio_dir, exist_ok=True)

    # Start processing any queued uploads from previous session
    print(f"Starting queue processing... Found {len(upload_queue)} queued tasks")
    await start_next_uploads()

    # Start background rate limit retry checker
    retry_checker_task = asyncio.create_task(check_and_retry_expired_rate_limits())
    print("Started background rate limit retry checker")

    yield

    # Shutdown - cancel background tasks
    retry_checker_task.cancel()
    try:
        await retry_checker_task
    except asyncio.CancelledError:
        pass

    # Shutdown
    print("Shutting down Video Upload Automation Hub")

# FastAPI app
app = FastAPI(title="Video Upload Automation Hub", version="1.0.0", lifespan=lifespan)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",
        "http://127.0.0.1:3000",
        "http://localhost:5173",
        "http://127.0.0.1:5173"
    ],
    allow_origin_regex=r"http://(localhost|127\.0\.0\.1)(:\\d+)?",
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# File-based storage functions
async def init_file_storage():
    """Initialize file-based storage"""
    try:
        os.makedirs(DATA_DIR, exist_ok=True)

        # Create empty tasks file if it doesn't exist
        if not os.path.exists(TASKS_FILE):
            async with aiofiles.open(TASKS_FILE, 'w') as f:
                await f.write('[]')

        print("File-based storage initialized successfully")

        # Load existing tasks into memory
        await load_tasks_into_memory()
        return True

    except Exception as e:
        print(f"Failed to initialize file storage: {e}")
        return False

async def load_tasks_into_memory():
    """Load tasks from file into memory dictionaries"""
    try:
        file_tasks_data = await load_tasks_from_file()

        for task_data in file_tasks_data:
            try:
                # Handle datetime conversion
                for key in ['created_at', 'started_at', 'completed_at', 'last_updated']:
                    if key in task_data and isinstance(task_data[key], str):
                        task_data[key] = datetime.fromisoformat(task_data[key].replace('Z', '+00:00'))

                task = UploadTask(**task_data)

                # Place task in appropriate dictionary based on status
                if task.status == UploadStatus.COMPLETED:
                    completed_uploads[task.id] = task
                elif task.status == UploadStatus.FAILED:
                    failed_uploads[task.id] = task
                elif task.status == UploadStatus.IN_PROGRESS:
                    active_uploads[task.id] = task
                elif task.status == UploadStatus.QUEUED:
                    upload_queue.append(task)

            except Exception as e:
                print(f"Error loading task {task_data.get('id', 'unknown')}: {e}")
                continue

        print(f"Loaded {len(completed_uploads)} completed, {len(failed_uploads)} failed, {len(active_uploads)} active, {len(upload_queue)} queued tasks from file")

    except Exception as e:
        print(f"Error loading tasks into memory: {e}")

async def save_task_to_file(task: UploadTask):
    """Save a single task to file"""
    try:
        # Load existing tasks
        tasks = await load_tasks_from_file()

        # Update or add task
        task_dict = task.dict()
        # Convert datetime objects to strings for JSON serialization
        for key, value in task_dict.items():
            if isinstance(value, datetime):
                task_dict[key] = value.isoformat()

        # Find and replace existing task or add new one
        task_updated = False
        for i, existing_task in enumerate(tasks):
            if existing_task.get('id') == task.id:
                tasks[i] = task_dict
                task_updated = True
                break

        if not task_updated:
            tasks.append(task_dict)

        # Save back to file
        async with aiofiles.open(TASKS_FILE, 'w') as f:
            await f.write(json.dumps(tasks, indent=2, default=str))

    except Exception as e:
        print(f"Error saving task to file: {e}")

async def load_tasks_from_file() -> List[Dict]:
    """Load all tasks from file"""
    try:
        async with aiofiles.open(TASKS_FILE, 'r') as f:
            content = await f.read()
            return json.loads(content) if content.strip() else []
    except Exception as e:
        print(f"Error loading tasks from file: {e}")
        return []


# Helper to save full tasks list back to file
async def save_tasks_to_file(tasks: List[Dict]):
    """Overwrite tasks.json with provided tasks list (handles datetime serialization)"""
    try:
        serializable = []
        for task in tasks:
            # If dicts contain datetime values as strings already, keep them; if datetime objects, convert
            converted = {}
            for k, v in task.items():
                if isinstance(v, datetime):
                    converted[k] = v.isoformat()
                elif isinstance(v, dict):
                    # Shallow copy for nested dicts (embed_codes etc.)
                    converted[k] = v
                else:
                    converted[k] = v
            serializable.append(converted)

        async with aiofiles.open(TASKS_FILE, 'w') as f:
            await f.write(json.dumps(serializable, indent=2, default=str))
    except Exception as e:
        print(f"Error saving tasks to file: {e}")

# WebSocket broadcast function
async def broadcast_task_update(task: UploadTask):
    """Broadcast task updates to all connected WebSocket clients"""
    if active_connections:
        message = {
            "type": "task_update",
            "data": task.dict()
        }
        disconnected = []
        for connection in active_connections:
            try:
                await connection.send_text(json.dumps(message, default=str))
            except:
                disconnected.append(connection)

        # Remove disconnected clients
        for conn in disconnected:
            if conn in active_connections:
                active_connections.remove(conn)

# Duplicate detection and prevention functions
async def check_for_duplicates(task: UploadTask) -> Dict[str, Any]:
    """Check if the same URL/file already exists on any platform (local + remote)"""
    duplicates_found = {}

    try:
        # STEP 1: Check in completed uploads first (fast local check)
        for completed_task in completed_uploads.values():
            if completed_task.url == task.url or completed_task.filename == task.filename:
                for platform, embed_code in completed_task.embed_codes.items():
                    if embed_code:  # If embed code exists, file was successfully uploaded
                        duplicates_found[platform] = {
                            "task_id": completed_task.id,
                            "embed_code": embed_code,
                            "file_code": completed_task.file_codes.get(platform, ""),
                            "uploaded_at": completed_task.completed_at.isoformat() if completed_task.completed_at else "Unknown",
                            "source": "memory"
                        }

        # STEP 2: Check in file storage for historical uploads (local storage)
        try:
            file_tasks_data = await load_tasks_from_file()
            for task_data in file_tasks_data:
                if (task_data.get('url') == task.url or
                    task_data.get('filename') == task.filename):

                    embed_codes = task_data.get('embed_codes', {})
                    file_codes = task_data.get('file_codes', {})

                    for platform, embed_code in embed_codes.items():
                        if embed_code and platform not in duplicates_found:
                            duplicates_found[platform] = {
                                "task_id": task_data.get('id', 'unknown'),
                                "embed_code": embed_code,
                                "file_code": file_codes.get(platform, ""),
                                "uploaded_at": task_data.get('completed_at', 'Unknown'),
                                "source": "file_storage"
                            }
        except Exception as e:
            print(f"Error checking file storage for duplicates: {e}")

        # STEP 3: Check remote platforms for existing uploads (slower but accurate)
        # Only check platforms not already found in local storage
        remaining_platforms = [p for p in Platform if p.value not in duplicates_found]

        if remaining_platforms:
            print(f"[DUPLICATE-CHECK] Checking {len(remaining_platforms)} platforms remotely...")

            async with aiohttp.ClientSession() as session:
                for platform in remaining_platforms:
                    try:
                        config = PLATFORM_CONFIGS[platform]
                        if not config["api_key"]:
                            continue

                        # Call platform-specific duplicate check
                        existing_check = None
                        if platform == Platform.LULUSTREAM:
                            existing_check = await check_lulustream_existing_upload(session, task, config)
                        elif platform == Platform.STREAMP2P:
                            existing_check = await check_streamp2p_existing_task(session, task, config)
                        elif platform == Platform.RPMSHARE:
                            existing_check = await check_rpmshare_existing_task(session, task, config)
                        elif platform == Platform.FILEMOON:
                            existing_check = await check_filemoon_existing_upload(session, task, config)
                        elif platform == Platform.UPNSHARE:
                            existing_check = await check_upnshare_existing_task(session, task, config)

                        if existing_check and existing_check.get("status") == "completed":
                            duplicates_found[platform.value] = {
                                "task_id": "remote_check",
                                "embed_code": existing_check.get("embed_code", ""),
                                "file_code": existing_check.get("file_code", ""),
                                "uploaded_at": "Remote check",
                                "source": "remote_api"
                            }
                            print(f"[DUPLICATE-CHECK] Found existing upload on {platform.value}")

                    except Exception as e:
                        print(f"Error checking {platform.value} for duplicates: {e}")

        return {
            "has_duplicates": len(duplicates_found) > 0,
            "duplicates": duplicates_found,
            "platforms_affected": list(duplicates_found.keys()),
            "total_duplicates": len(duplicates_found)
        }

    except Exception as e:
        print(f"Error in duplicate detection: {e}")
        return {
            "has_duplicates": False,
            "duplicates": {},
            "platforms_affected": [],
            "total_duplicates": 0,
            "error": str(e)
        }

async def get_resumable_task_data(task: UploadTask) -> Dict[str, Any]:
    """Check if this task can be resumed from a previous interrupted upload"""
    try:
        # Check active uploads first
        for active_task in active_uploads.values():
            if (active_task.url == task.url and
                active_task.id != task.id):
                return {
                    "can_resume": True,
                    "existing_task": active_task,
                    "reason": "Upload already in progress"
                }

        # Check failed uploads that might be resumable
        for failed_task in failed_uploads.values():
            if (failed_task.url == task.url and
                failed_task.id != task.id):

                # Check if any platforms succeeded
                successful_platforms = []
                for platform, status in failed_task.platform_status.items():
                    if status == "completed" and failed_task.embed_codes.get(platform):
                        successful_platforms.append(platform)

                if successful_platforms:
                    return {
                        "can_resume": True,
                        "existing_task": failed_task,
                        "successful_platforms": successful_platforms,
                        "reason": f"Partial success on {len(successful_platforms)} platforms"
                    }

        return {"can_resume": False}

    except Exception as e:
        print(f"Error checking resumable tasks: {e}")
        return {"can_resume": False, "error": str(e)}

async def check_platform_for_existing_file(session: aiohttp.ClientSession, platform: Platform, task: UploadTask, config: dict) -> Dict[str, Any]:
    """Check if file already exists on specific platform"""
    try:
        # Check if we already have embed codes for this platform from previous uploads
        existing_embed = task.embed_codes.get(platform.value)
        existing_file_code = task.file_codes.get(platform.value)

        if existing_embed and existing_file_code:
            return {
                "exists": True,
                "file_code": existing_file_code,
                "embed_code": existing_embed,
                "source": "task_data"
            }

        # For platforms that support file listing/search, we could add API calls here
        # For now, we rely on our internal tracking
        return {"exists": False}

    except Exception as e:
        print(f"Error checking existing file on {platform.value}: {e}")
        return {"exists": False, "error": str(e)}

# Platform-specific duplicate checking functions
async def check_lulustream_existing_upload(session: aiohttp.ClientSession, task: UploadTask, config: dict) -> Dict[str, Any]:
    """Accurately detect existing file on Lulustream using URL uploads and file list/info"""
    try:
        # 1) Check current URL uploads queue for exact remote_url match
        url_uploads = f"{config['base_url']}/file/url_uploads"
        params = {"key": config["api_key"]}
        async with session.get(url_uploads, params=params, timeout=aiohttp.ClientTimeout(total=30)) as response:
            if response.status == 200:
                result = await response.json()
                # Handle both dict and string responses
                if isinstance(result, dict) and result.get("result"):
                    uploads = result["result"]
                    if isinstance(uploads, list):
                        for upload in uploads:
                            if upload.get("remote_url") == task.url and upload.get("file_code"):
                                file_code = upload["file_code"]
                                # Verify via file/info (should be 200 when exists)
                                info_url = f"{config['base_url']}{config['info_endpoint']}"
                                info_params = {"key": config["api_key"], "file_code": file_code}
                                async with session.get(info_url, params=info_params) as info_resp:
                                    if info_resp.status == 200:
                                        info = await info_resp.json()
                                        if info.get("status") == 200:
                                            embed_code = config["embed_template"].format(file_code=file_code)
                                            print(f"LuluStream: Found existing URL upload - {file_code}")
                                            return {"status": "completed", "file_code": file_code, "embed_code": embed_code, "source": "url_uploads"}

        # 2) IMPROVED: Comprehensive file search with multiple strategies
        url_filename = task.url.split('/')[-1].split('?')[0]
        base_filename = task.filename.rsplit('.', 1)[0] if '.' in task.filename else task.filename

        # Multiple search terms for better matching
        search_terms = [
            task.filename,
            url_filename,
            base_filename,
            task.filename[:30] if len(task.filename) > 30 else None
        ]

        list_url = f"{config['base_url']}/file/list"

        for search_term in search_terms:
            if not search_term:
                continue

            list_params = {
                "key": config["api_key"],
                "title": search_term,
                "per_page": 50,
                "created": 2880  # last 48h
            }

            async with session.get(list_url, params=list_params, timeout=aiohttp.ClientTimeout(total=15)) as resp:
                if resp.status == 200:
                    result = await resp.json()
                    # Handle both dict and string responses
                    if isinstance(result, dict) and result.get("status") == 200:
                        result_data = result.get("result", {})
                        if isinstance(result_data, dict) and result_data.get("files"):
                            files = result_data["files"]
                        elif isinstance(result_data, list):
                            files = result_data
                        else:
                            continue

                        for file_info in files:
                            file_title = file_info.get("title", "")
                            file_code = file_info.get("file_code")

                            if not file_code:
                                continue

                            # Enhanced matching strategies
                            title_matches = [
                                file_title == task.filename,
                                file_title == url_filename,
                                file_title.rsplit('.', 1)[0] == base_filename if '.' in file_title else False,
                                base_filename in file_title and len(base_filename) > 10
                            ]

                            if any(title_matches):
                                embed_code = config["embed_template"].format(file_code=file_code)
                                print(f"LuluStream: Found existing file - {file_code} ({file_title})")
                                return {"status": "completed", "file_code": file_code, "embed_code": embed_code, "source": "file_list"}
        return None
    except Exception as e:
        print(f"Error checking LuluStream existing uploads: {e}")
        return None

async def check_streamp2p_existing_task(session: aiohttp.ClientSession, task: UploadTask, config: dict) -> Dict[str, Any]:
    """Check if URL already exists in StreamP2P advance-upload tasks"""
    try:
        # Get list of existing advance-upload tasks
        list_url = f"{config['base_url']}{config['upload_endpoint']}"
        headers = {"api-token": config['api_key']}

        async with session.get(list_url, headers=headers, timeout=aiohttp.ClientTimeout(total=30)) as response:
            if response.status == 200:
                result = await response.json()

                # Check if our URL already exists as a task
                for task_data in result.get("data", []):
                    # Check by URL in name field (StreamP2P stores URL in name field)
                    task_name = task_data.get("name", "")
                    task_status = task_data.get("status", "")

                    if task_name == task.url and task_status in ["Completed", "Processing", "Queued"]:
                        task_id = task_data.get("id")
                        videos = task_data.get("videos", [])

                        if videos and task_status == "Completed":
                            # Use first video ID as file code
                            file_code = videos[0] if isinstance(videos, list) else str(videos)
                            embed_code = config["embed_template"].format(file_code=file_code)
                            print(f"StreamP2P: Found existing completed task - {file_code} for URL: {task.url}")
                            return {
                                "file_code": file_code,
                                "embed_code": embed_code,
                                "status": "completed",
                                "source": "existing_task",
                                "task_id": task_id
                            }
                        elif task_status in ["Processing", "Queued"]:
                            print(f"StreamP2P: Found existing {task_status.lower()} task for URL: {task.url}")
                            # Return a pending status to avoid duplicate submission
                            return {
                                "status": "pending",
                                "task_id": task_id,
                                "message": f"Task already exists with status: {task_status}"
                            }

        return None  # No existing task found

    except Exception as e:
        print(f"Error checking StreamP2P existing tasks: {e}")
        return None

async def check_rpmshare_existing_task(session: aiohttp.ClientSession, task: UploadTask, config: dict) -> Dict[str, Any]:
    """Check if URL already exists in RPMShare advance-upload tasks"""
    try:
        # Get list of existing advance-upload tasks
        list_url = f"{config['base_url']}{config['upload_endpoint']}"
        headers = {"api-token": config['api_key']}

        async with session.get(list_url, headers=headers, timeout=aiohttp.ClientTimeout(total=30)) as response:
            if response.status == 200:
                result = await response.json()

                # Check if our URL already exists as a task
                for task_data in result.get("data", []):
                    # Check by URL in name field (RPMShare stores URL in name field)
                    task_name = task_data.get("name", "")
                    task_status = task_data.get("status", "")

                    if task_name == task.url and task_status in ["Completed", "Processing", "Queued"]:
                        task_id = task_data.get("id")
                        videos = task_data.get("videos", [])

                        if videos and task_status == "Completed":
                            # Use first video ID as file code
                            file_code = videos[0] if isinstance(videos, list) else str(videos)
                            embed_code = config["embed_template"].format(file_code=file_code)
                            print(f"RPMShare: Found existing completed task - {file_code} for URL: {task.url}")
                            return {
                                "file_code": file_code,
                                "embed_code": embed_code,
                                "status": "completed",
                                "source": "existing_task",
                                "task_id": task_id
                            }
                        elif task_status in ["Processing", "Queued"]:
                            print(f"RPMShare: Found existing {task_status.lower()} task for URL: {task.url}")
                            # Return a pending status to avoid duplicate submission
                            return {
                                "status": "pending",
                                "task_id": task_id,
                                "message": f"Task already exists with status: {task_status}"
                            }

        return None  # No existing task found

    except Exception as e:
        print(f"Error checking RPMShare existing tasks: {e}")
        return None

async def check_filemoon_existing_upload(session: aiohttp.ClientSession, task: UploadTask, config: dict) -> Dict[str, Any]:
    """Accurately detect existing file on FileMoon using File List and File Info APIs"""
    try:
        # Extract filename variations for matching
        url_filename = task.url.split('/')[-1].split('?')[0]
        base_filename = task.filename.rsplit('.', 1)[0] if '.' in task.filename else task.filename

        # Search strategies: recent files first, then by title
        search_params_list = [
            {
                "key": config["api_key"],
                "per_page": 50,
                "created": 60,  # last hour
            },
            {
                "key": config["api_key"],
                "per_page": 100,
                "created": 1440,  # last 24h
                "title": base_filename[:50]  # search by title
            }
        ]

        list_url = f"{config['base_url']}{config['list_endpoint']}"

        for params in search_params_list:
            async with session.get(list_url, params=params, timeout=aiohttp.ClientTimeout(total=30)) as response:
                if response.status == 200:
                    result = await response.json()
                    # Handle both dict and string responses
                    if isinstance(result, dict) and result.get("status") == 200 and result.get("result"):
                        files = result["result"]
                        if not isinstance(files, list):
                            continue

                        for file_info in files:
                            file_name = file_info.get("name") or file_info.get("title") or ""
                            file_code = file_info.get("filecode") or file_info.get("file_code")

                            if not file_code:
                                continue

                            # Multiple matching strategies
                            name_matches = [
                                file_name == task.filename,
                                file_name == url_filename,
                                file_name.rsplit('.', 1)[0] == base_filename if '.' in file_name else False,
                                base_filename in file_name and len(base_filename) > 10  # partial match for long names
                            ]

                            if any(name_matches):
                                # Verify file exists and is playable using File Info API
                                info_url = f"{config['base_url']}{config['info_endpoint']}"
                                info_params = {"key": config["api_key"], "file_code": file_code}

                                async with session.get(info_url, params=info_params, timeout=aiohttp.ClientTimeout(total=15)) as info_resp:
                                    if info_resp.status == 200:
                                        info_result = await info_resp.json()
                                        if info_result.get("status") == 200:
                                            file_data = info_result.get("result", [])
                                            if file_data and isinstance(file_data, list) and len(file_data) > 0:
                                                file_details = file_data[0]
                                                canplay = file_details.get("canplay", 0)

                                                # File exists and is ready
                                                if canplay == 1:
                                                    embed_code = f'<iframe src="https://filemoon.to/e/{file_code}" frameborder="0" marginwidth="0" marginheight="0" scrolling="no" width="640" height="360" allowfullscreen></iframe>'
                                                    print(f"FileMoon: Found existing playable file - {file_code} ({file_name})")
                                                    return {
                                                        "status": "completed",
                                                        "file_code": file_code,
                                                        "embed_code": embed_code,
                                                        "source": "existing_file"
                                                    }
                                                else:
                                                    print(f"FileMoon: Found file but not playable yet - {file_code} (canplay={canplay})")

        return None
    except Exception as e:
        print(f"Error checking FileMoon existing uploads: {e}")
        return None

async def check_upnshare_existing_task(session: aiohttp.ClientSession, task: UploadTask, config: dict) -> Dict[str, Any]:
    """Check if URL already exists in UpnShare advance-upload tasks"""
    try:
        # Get list of existing advance-upload tasks
        list_url = f"{config['base_url']}{config['upload_endpoint']}"
        headers = {"api-token": config['api_key']}

        async with session.get(list_url, headers=headers, timeout=aiohttp.ClientTimeout(total=30)) as response:
            if response.status == 200:
                result = await response.json()

                # Check if our URL already exists as a task
                for task_data in result.get("data", []):
                    # Check by URL in name field (UpnShare stores URL in name field)
                    task_name = task_data.get("name", "")
                    task_status = task_data.get("status", "")

                    if task_name == task.url and task_status in ["Completed", "Processing", "Queued"]:
                        task_id = task_data.get("id")
                        videos = task_data.get("videos", [])

                        if videos and task_status == "Completed":
                            # Use first video ID as file code
                            file_code = videos[0] if isinstance(videos, list) else str(videos)
                            embed_code = config["embed_template"].format(file_code=file_code)
                            print(f"UpnShare: Found existing completed task - {file_code} for URL: {task.url}")
                            return {
                                "file_code": file_code,
                                "embed_code": embed_code,
                                "status": "completed",
                                "source": "existing_task",
                                "task_id": task_id
                            }
                        elif task_status in ["Processing", "Queued"]:
                            print(f"UpnShare: Found existing {task_status.lower()} task for URL: {task.url}")
                            # Return a pending status to avoid duplicate submission
                            return {
                                "status": "pending",
                                "task_id": task_id,
                                "message": f"Task already exists with status: {task_status}"
                            }

        return None  # No existing task found

    except Exception as e:
        print(f"Error checking UpnShare existing tasks: {e}")
        return None

# Platform upload functions
async def upload_to_lulustream(session: aiohttp.ClientSession, task: UploadTask, config: dict, cancel_event: Optional[asyncio.Event]) -> Dict[str, Any]:
    """Upload to Lulustream using their API with proper duplicate prevention"""
    try:
        # STEP 1: Check if URL already exists in LuluStream uploads
        existing_upload = await check_lulustream_existing_upload(session, task, config)
        if existing_upload:
            print(f"LuluStream: Found existing upload for URL - {existing_upload['file_code']}")
            return existing_upload

        print(f"LuluStream: No existing upload found, creating new upload for: {task.url}")

        # STEP 2: Submit upload request (NO URL modification to prevent duplicates)
        upload_url = f"{config['base_url']}{config['upload_endpoint']}"
        params = {
            "key": config["api_key"],
            "url": task.url,  # Use original URL - NO timestamp modification
            "file_public": 1,
            "fld_id": 0
        }

        await _update_progress(task, Platform.LULUSTREAM, 1, 10, 5, "uploading")

        async with session.get(upload_url, params=params, timeout=aiohttp.ClientTimeout(total=120)) as response:
            # Check for rate limiting or server errors
            if await handle_http_error("lulustream", response.status, await response.text() if response.status != 200 else ""):
                # Add to retry queue for rate limiting
                await add_rate_limited_task("lulustream", task.id, {
                    "filename": task.filename,
                    "url": task.url,
                    "retry_reason": f"HTTP {response.status} Error"
                })
                return {"error": f"HTTP {response.status}: Rate limited - added to retry queue", "status": "rate_limited"}

            if response.status == 200:
                result = await response.json()

                if result.get("status") == 200 and result.get("result"):
                    file_code = None
                    if isinstance(result["result"], dict):
                        file_code = result["result"].get("filecode") or result["result"].get("file_code")
                    elif isinstance(result["result"], str):
                        file_code = result["result"]

                    if file_code:
                        await _update_progress(task, Platform.LULUSTREAM, 3, 10, 5, "processing")

                        # Step 2: Poll for completion
                        max_attempts = 60
                        for attempt in range(max_attempts):
                            if cancel_event and cancel_event.is_set():
                                return {"error": "Upload cancelled"}

                            # Check file info
                            info_url = f"{config['base_url']}{config['info_endpoint']}"
                            info_params = {"key": config["api_key"], "file_code": file_code}

                            async with session.get(info_url, params=info_params) as info_response:
                                if info_response.status == 200:
                                    info_result = await info_response.json()

                                    if info_result.get("status") == 200 and info_result.get("result"):
                                        file_info = info_result["result"][0] if isinstance(info_result["result"], list) else info_result["result"]

                                        can_play = file_info.get("canplay")
                                        status = file_info.get("status")
                                        title = file_info.get("title", "")

                                        # Check for DMCA false positive
                                        dmca_indicators = [
                                            "dmca violation reported",
                                            "dmca violation",
                                            "copyright violation",
                                            "content removed",
                                            "violation reported"
                                        ]

                                        is_dmca_flagged = any(indicator in title.lower() for indicator in dmca_indicators)

                                        if is_dmca_flagged:
                                            # DMCA false positive detected
                                            print(f"[DMCA-DETECTED] LuluStream: {task.filename} - {title}")

                                            # Still generate embed code but mark as DMCA flagged
                                            embed_code = config["embed_template"].format(file_code=file_code)
                                            await _update_progress(task, Platform.LULUSTREAM, 10, 10, 5, "completed")
                                            await _save_embed_code(task, Platform.LULUSTREAM, embed_code, file_code, task.filename)

                                            # Broadcast DMCA notification
                                            await broadcast_dmca_notification(task, file_code, title)

                                            return {
                                                "file_code": file_code,
                                                "embed_code": embed_code,
                                                "status": "completed",
                                                "dmca_flagged": True,
                                                "dmca_message": title
                                            }

                                        elif can_play in [1, "1", True] or status in ["ready", "completed", "active"]:
                                            # Normal successful upload - schedule post-upload tasks
                                            embed_code = config["embed_template"].format(file_code=file_code)
                                            await _update_progress(task, Platform.LULUSTREAM, 10, 10, 5, "completed")
                                            await _save_embed_code(task, Platform.LULUSTREAM, embed_code, file_code, task.filename)

                                            # Schedule Lulustream-specific post-upload tasks (non-blocking)
                                            asyncio.create_task(handle_lulustream_post_upload_tasks(session, task, config, file_code))

                                            return {"file_code": file_code, "embed_code": embed_code, "status": "completed"}

                            # Update progress during polling
                            current_step = min(3 + (attempt // 3), 9)
                            await _update_progress(task, Platform.LULUSTREAM, current_step, 10, 5, "processing")
                            await asyncio.sleep(5)

                        # Timeout - verify upload exists before marking as completed
                        verification_result = await verify_upload_on_platform(session, Platform.LULUSTREAM, [file_code], config, task)
                        if verification_result.get("status") == "completed":
                            await _update_progress(task, Platform.LULUSTREAM, 10, 10, 5, "completed")
                            await _save_embed_code(task, Platform.LULUSTREAM, verification_result["embed_code"], verification_result["file_code"], task.filename)
                            return verification_result
                        else:
                            return {"error": f"Upload verification failed after timeout: {verification_result.get('error', 'Unknown error')}"}

                # If no file code found, try to verify upload by searching for potential file codes
                print(f"Lulustream: No file code found, attempting verification by searching platform")
                await _update_progress(task, Platform.LULUSTREAM, 8, 10, 5, "verifying")

                # Generate potential file codes to search for
                potential_codes = [
                    f"{task.id[:8]}_{timestamp}",
                    f"{task.id[:12]}",
                    timestamp,
                    result.get("msg", "").split()[-1] if result.get("msg") else None
                ]
                potential_codes = [code for code in potential_codes if code]  # Remove None values

                # Try to verify upload exists on platform
                verification_result = await verify_upload_on_platform(session, Platform.LULUSTREAM, potential_codes, config, task)

                if verification_result.get("status") == "completed":
                    await _update_progress(task, Platform.LULUSTREAM, 10, 10, 5, "completed")
                    await _save_embed_code(task, Platform.LULUSTREAM, verification_result["embed_code"], verification_result["file_code"], task.filename)
                    return verification_result
                else:
                    return {"error": f"Upload verification failed: {verification_result.get('error', 'Unknown error')}"}
            else:
                return {"error": f"HTTP {response.status}: Upload request failed"}

    except Exception as e:
        return {"error": f"Exception during upload: {str(e)}"}

async def handle_lulustream_post_upload_tasks(session: aiohttp.ClientSession, task: UploadTask, config: dict, primary_file_code: str):
    """
    Handle Lulustream-specific post-upload tasks:
    1. Find and verify the best working file from duplicates
    2. Delete non-working duplicate files
    3. Detect and report DMCA false positives
    """
    try:
        print(f"[LULUSTREAM-POST] Starting post-upload tasks for {task.filename}")

        # Wait for encoding to complete before checking duplicates
        await asyncio.sleep(30)  # Give time for initial encoding

        # Step 1: Find all files with similar names (duplicates)
        duplicate_files = await find_lulustream_duplicates(session, task, config, primary_file_code)

        if len(duplicate_files) > 1:
            print(f"[LULUSTREAM-POST] Found {len(duplicate_files)} duplicate files")

            # Step 2: Test each file to find the best working one
            best_file = await find_best_working_lulustream_file(session, config, duplicate_files)

            if best_file:
                print(f"[LULUSTREAM-POST] Best working file: {best_file['file_code']}")

                # Step 3: Update CSV with the best file if different from primary
                if best_file['file_code'] != primary_file_code:
                    await update_csv_with_best_file(task, Platform.LULUSTREAM, best_file)

                # Step 4: Delete duplicate files (except the best one)
                await delete_lulustream_duplicates(session, config, duplicate_files, best_file['file_code'])
            else:
                print(f"[LULUSTREAM-POST] No working files found among duplicates")

        # Step 5: Check for DMCA false positives
        await check_lulustream_dmca_false_positives(session, config, [primary_file_code])

    except Exception as e:
        print(f"[LULUSTREAM-POST] Error in post-upload tasks: {e}")

async def upload_to_streamp2p(session: aiohttp.ClientSession, task: UploadTask, config: dict, cancel_event: Optional[asyncio.Event]) -> Dict[str, Any]:
    """Upload to StreamP2P using their API with proper duplicate prevention"""
    try:
        # STEP 1: Check if URL already exists in StreamP2P advance-upload tasks
        existing_task = await check_streamp2p_existing_task(session, task, config)
        if existing_task:
            print(f"StreamP2P: Found existing task for URL - {existing_task.get('id', 'unknown')}")
            return existing_task

        print(f"StreamP2P: No existing task found, creating new advance-upload task for: {task.url}")

        # STEP 2: Create new advance-upload task (NO URL modification)
        upload_url = f"{config['base_url']}{config['upload_endpoint']}"
        headers = {
            "api-token": config['api_key'],
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
        data = {
            "url": task.url,  # Use original URL - NO timestamp modification
            "name": task.filename  # Use 'name' field as per API spec
        }

        await _update_progress(task, Platform.STREAMP2P, 1, 12, 5, "uploading")

        async with session.post(upload_url, headers=headers, json=data, timeout=aiohttp.ClientTimeout(total=300)) as response:
            if response.status in (200, 201):
                result = await response.json()
                print(f"StreamP2P upload response: {result}")  # Debug logging

                # Try multiple ways to extract upload ID
                upload_id = (result.get("id") or
                           result.get("data", {}).get("id") or
                           result.get("upload_id") or
                           result.get("file_id") or
                           result.get("video_id"))

                # If no upload ID but response indicates success, try alternative approach
                if not upload_id and result.get("status") == "success":
                    # Generate a fallback ID based on response data
                    upload_id = result.get("message", "").split()[-1] if result.get("message") else None

                if upload_id:
                    await _update_progress(task, Platform.STREAMP2P, 3, 12, 5, "processing")

                    # Step 2: Poll for task completion using correct API flow
                    max_attempts = 60
                    for attempt in range(max_attempts):
                        if cancel_event and cancel_event.is_set():
                            return {"error": "Upload cancelled"}

                        try:
                            # Check task status using correct endpoint
                            status_url = f"{config['base_url']}{config['status_endpoint']}/{upload_id}"
                            async with session.get(status_url, headers=headers) as status_response:
                                if status_response.status == 200:
                                    status_result = await status_response.json()
                                    task_status = status_result.get("status", "").lower()

                                    if task_status in ("completed", "ready", "success"):
                                        # Extract video IDs from completed task
                                        videos = status_result.get("videos", [])
                                        if videos:
                                            # Use first video ID as file code
                                            file_code = videos[0] if isinstance(videos, list) else str(videos)
                                            embed_code = config["embed_template"].format(file_code=file_code)
                                            await _update_progress(task, Platform.STREAMP2P, 12, 12, 5, "completed")
                                            await _save_embed_code(task, Platform.STREAMP2P, embed_code, file_code, task.filename)
                                            return {"file_code": file_code, "embed_code": embed_code, "status": "completed"}
                                        else:
                                            print(f"StreamP2P: Task completed but no videos found in response")

                                    elif task_status in ("failed", "error"):
                                        error_msg = status_result.get("error", "Upload failed")
                                        return {"error": f"Upload failed: {error_msg}"}

                        except Exception as e:
                            print(f"StreamP2P status check error: {e}")

                        # Update progress
                        current_step = min(3 + (attempt // 3), 11)
                        await _update_progress(task, Platform.STREAMP2P, current_step, 12, 5, "processing")
                        await asyncio.sleep(5)

                # If no upload ID found, return error
                return {"error": "No upload ID received from StreamP2P"}

            else:
                response_text = await response.text()
                print(f"StreamP2P upload failed: {response.status} - {response_text}")
                return {"error": f"HTTP {response.status}: Upload request failed"}

    except Exception as e:
        print(f"StreamP2P upload exception: {e}")
        return {"error": f"Exception during upload: {str(e)}"}

async def upload_to_rpmshare(session: aiohttp.ClientSession, task: UploadTask, config: dict, cancel_event: Optional[asyncio.Event]) -> Dict[str, Any]:
    """Upload to RPMShare using their API with proper duplicate prevention"""
    try:
        # STEP 1: Check if URL already exists in RPMShare advance-upload tasks
        existing_task = await check_rpmshare_existing_task(session, task, config)
        if existing_task:
            print(f"RPMShare: Found existing task for URL - {existing_task.get('id', 'unknown')}")
            return existing_task

        print(f"RPMShare: No existing task found, creating new advance-upload task for: {task.url}")

        # STEP 2: Create new advance-upload task (NO URL modification)
        upload_url = f"{config['base_url']}{config['upload_endpoint']}"
        headers = {
            "api-token": config['api_key'],
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
        data = {
            "url": task.url,  # Use original URL - NO timestamp modification
            "name": task.filename  # Use 'name' field as per API spec
        }

        await _update_progress(task, Platform.RPMSHARE, 1, 12, 5, "uploading")

        async with session.post(upload_url, headers=headers, json=data, timeout=aiohttp.ClientTimeout(total=300)) as response:
            if response.status in (200, 201):
                result = await response.json()
                print(f"RPMShare upload response: {result}")  # Debug logging

                # Try multiple ways to extract upload ID
                upload_id = (result.get("id") or
                           result.get("data", {}).get("id") or
                           result.get("upload_id") or
                           result.get("file_id") or
                           result.get("video_id"))

                # If no upload ID but response indicates success, try alternative approach
                if not upload_id and result.get("status") == "success":
                    # Generate a fallback ID based on response data
                    upload_id = result.get("message", "").split()[-1] if result.get("message") else None

                if upload_id:
                    await _update_progress(task, Platform.RPMSHARE, 3, 12, 5, "processing")

                    # Step 2: Poll for task completion using correct API flow
                    max_attempts = 60
                    for attempt in range(max_attempts):
                        if cancel_event and cancel_event.is_set():
                            return {"error": "Upload cancelled"}

                        try:
                            # Check task status using correct endpoint
                            status_url = f"{config['base_url']}{config['status_endpoint']}/{upload_id}"
                            async with session.get(status_url, headers=headers) as status_response:
                                if status_response.status == 200:
                                    status_result = await status_response.json()
                                    task_status = status_result.get("status", "").lower()

                                    if task_status in ("completed", "ready", "success"):
                                        # Extract video IDs from completed task
                                        videos = status_result.get("videos", [])
                                        if videos:
                                            # Use first video ID as file code
                                            file_code = videos[0] if isinstance(videos, list) else str(videos)
                                            embed_code = config["embed_template"].format(file_code=file_code)
                                            await _update_progress(task, Platform.RPMSHARE, 12, 12, 5, "completed")
                                            await _save_embed_code(task, Platform.RPMSHARE, embed_code, file_code, task.filename)
                                            return {"file_code": file_code, "embed_code": embed_code, "status": "completed"}
                                        else:
                                            print(f"RPMShare: Task completed but no videos found in response")

                                    elif task_status in ("failed", "error"):
                                        error_msg = status_result.get("error", "Upload failed")
                                        return {"error": f"Upload failed: {error_msg}"}

                        except Exception as e:
                            print(f"RPMShare status check error: {e}")

                        # Update progress
                        current_step = min(3 + (attempt // 3), 11)
                        await _update_progress(task, Platform.RPMSHARE, current_step, 12, 5, "processing")
                        await asyncio.sleep(5)

                # If no upload ID found, return error
                return {"error": "No upload ID received from RPMShare"}

            else:
                response_text = await response.text()
                print(f"RPMShare upload failed: {response.status} - {response_text}")
                return {"error": f"HTTP {response.status}: Upload request failed"}

    except Exception as e:
        print(f"RPMShare upload exception: {e}")
        return {"error": f"Exception during upload: {str(e)}"}

async def upload_to_filemoon(session: aiohttp.ClientSession, task: UploadTask, config: dict, cancel_event: Optional[asyncio.Event]) -> Dict[str, Any]:
    """Upload to FileMoon using their API with proper duplicate prevention"""
    try:
        # STEP 1: Check if URL already exists in FileMoon remote uploads
        existing_upload = await check_filemoon_existing_upload(session, task, config)
        if existing_upload:
            print(f"FileMoon: Found existing remote upload for URL - {existing_upload['file_code']}")
            return existing_upload

        print(f"FileMoon: No existing remote upload found, creating new upload for: {task.url}")

        # STEP 2: Submit remote upload (NO URL modification)
        upload_url = f"{config['base_url']}{config['upload_endpoint']}"
        params = {
            "key": config["api_key"],
            "url": task.url  # Use original URL - NO timestamp modification
        }

        await _update_progress(task, Platform.FILEMOON, 1, 20, 5, "uploading")

        async with session.get(upload_url, params=params, timeout=aiohttp.ClientTimeout(total=120)) as response:
            # Check for rate limiting or server errors
            if await handle_http_error("filemoon", response.status, await response.text() if response.status != 200 else ""):
                # Add to retry queue for rate limiting
                await add_rate_limited_task("filemoon", task.id, {
                    "filename": task.filename,
                    "url": task.url,
                    "retry_reason": f"HTTP {response.status} Error"
                })
                return {"error": f"HTTP {response.status}: Rate limited - added to retry queue", "status": "rate_limited"}

            if response.status == 200:
                result = await response.json()

                if result.get("status") == 200 and result.get("result", {}).get("filecode"):
                    file_code = result["result"]["filecode"]
                    await _update_progress(task, Platform.FILEMOON, 3, 20, 5, "processing")

                    # Step 2: Monitor remote upload status
                    max_attempts = 120
                    for attempt in range(max_attempts):
                        if cancel_event and cancel_event.is_set():
                            return {"error": "Upload cancelled"}

                        status_url = f"{config['base_url']}{config['status_endpoint']}"
                        status_params = {"key": config["api_key"], "file_code": file_code}

                        async with session.get(status_url, params=status_params) as status_response:
                            if status_response.status == 200:
                                status_result = await status_response.json()

                                if status_result.get("status") == 200:
                                    result_data = status_result.get("result", {})
                                    upload_status = result_data.get("status")
                                    progress = result_data.get("progress", "0%")

                                    print(f"FileMoon status: {upload_status}, progress: {progress}")

                                    # IMPROVED: Check if file exists via File Info API (ignore canplay status)
                                    info_url = f"{config['base_url']}{config['info_endpoint']}"
                                    info_params = {"key": config["api_key"], "file_code": file_code}

                                    try:
                                        async with session.get(info_url, params=info_params, timeout=aiohttp.ClientTimeout(total=10)) as info_response:
                                            if info_response.status == 200:
                                                info_result = await info_response.json()
                                                if info_result.get("status") == 200 and info_result.get("result"):
                                                    file_info = info_result["result"][0] if isinstance(info_result["result"], list) else info_result["result"]

                                                    # ✅ FIXED: File exists when status=200, regardless of canplay value
                                                    if file_info.get("status") == 200:
                                                        real_filename = file_info.get("name", task.filename)
                                                        canplay = file_info.get("canplay", 0)

                                                        print(f"FileMoon: File exists - {file_code} (canplay={canplay})")

                                                        # Generate correct FileMoon embed code
                                                        embed_code = f'<iframe src="https://filemoon.to/e/{file_code}" frameborder="0" marginwidth="0" marginheight="0" scrolling="no" width="640" height="360" allowfullscreen></iframe>'

                                                        await _update_progress(task, Platform.FILEMOON, 20, 20, 5, "completed")
                                                        await _save_embed_code(task, Platform.FILEMOON, embed_code, file_code, real_filename)
                                                        print(f"FileMoon: Upload completed - {file_code} (file exists, canplay={canplay})")
                                                        return {"file_code": file_code, "embed_code": embed_code, "status": "completed"}
                                                    else:
                                                        print(f"FileMoon: File not registered yet (status={file_info.get('status')})")
                                    except Exception as e:
                                        print(f"FileMoon: Error checking file info during polling: {e}")

                                    # Handle upload status - only fail on explicit errors
                                    if upload_status in ["ERROR", "FAILED"]:
                                        error_msg = result_data.get("error_msg", "Upload failed")
                                        return {"error": f"Upload failed: {error_msg}"}

                                    # Continue polling for WORKING, COMPLETED, or any other status
                                    # FileMoon might be slow to update status but file could be ready

                        # Update progress
                        current_step = min(3 + (attempt // 6), 19)
                        await _update_progress(task, Platform.FILEMOON, current_step, 20, 5, "processing")
                        await asyncio.sleep(5)

                    # Timeout - check if file was actually uploaded by verifying file info
                    print(f"FileMoon: Upload timed out, checking if file exists: {file_code}")

                    info_url = f"{config['base_url']}{config['info_endpoint']}"
                    info_params = {"key": config["api_key"], "file_code": file_code}

                    try:
                        async with session.get(info_url, params=info_params, timeout=aiohttp.ClientTimeout(total=10)) as info_response:
                            if info_response.status == 200:
                                info_result = await info_response.json()
                                if info_result.get("status") == 200 and info_result.get("result"):
                                    file_info = info_result["result"][0] if isinstance(info_result["result"], list) else info_result["result"]

                                    # ✅ FIXED: File exists when status=200, regardless of canplay value
                                    if file_info.get("status") == 200:
                                        real_filename = file_info.get("name", task.filename)
                                        canplay = file_info.get("canplay", 0)

                                        # Generate correct FileMoon embed code
                                        embed_code = f'<iframe src="https://filemoon.to/e/{file_code}" frameborder="0" marginwidth="0" marginheight="0" scrolling="no" width="640" height="360" allowfullscreen></iframe>'

                                        await _update_progress(task, Platform.FILEMOON, 20, 20, 5, "completed")
                                        await _save_embed_code(task, Platform.FILEMOON, embed_code, file_code, real_filename)
                                        print(f"FileMoon: Upload completed after timeout - {file_code} (file exists, canplay={canplay})")
                                        return {"file_code": file_code, "embed_code": embed_code, "status": "completed"}
                                    else:
                                        return {"error": f"FileMoon: File not registered on platform (status: {file_info.get('status')})"}
                                else:
                                    return {"error": f"FileMoon: File not found on platform (file_code: {file_code})"}
                            else:
                                return {"error": f"FileMoon: Failed to check file info (HTTP {info_response.status})"}
                    except Exception as e:
                        return {"error": f"FileMoon: Error checking file info: {e}"}

                # If no file code found, return error
                return {"error": "No file code received from FileMoon"}
            else:
                return {"error": f"HTTP {response.status}: Upload request failed"}

    except Exception as e:
        return {"error": f"Exception during upload: {str(e)}"}

async def upload_to_upnshare(session: aiohttp.ClientSession, task: UploadTask, config: dict, cancel_event: Optional[asyncio.Event]) -> Dict[str, Any]:
    """Upload to UpnShare using their API with proper duplicate prevention and rate limiting"""
    try:
        # STEP 1: Check if URL already exists in UpnShare advance-upload tasks
        existing_task = await check_upnshare_existing_task(session, task, config)
        if existing_task:
            print(f"UpnShare: Found existing task for URL - {existing_task.get('id', 'unknown')}")
            return existing_task

        print(f"UpnShare: No existing task found, creating new advance-upload task for: {task.url}")

        # STEP 2: Add rate limiting delay to prevent HTTP 429 errors
        rate_limit_delay = config.get('rate_limit_delay', 10)
        await asyncio.sleep(rate_limit_delay)

        # STEP 3: Create new advance-upload task (NO URL modification)
        upload_url = f"{config['base_url']}{config['upload_endpoint']}"
        headers = {
            "api-token": config['api_key'],
            "Content-Type": "application/json"
        }
        data = {
            "url": task.url,  # Use original URL - NO timestamp modification
            "name": task.filename  # Use 'name' field as per API spec
        }

        await _update_progress(task, Platform.UPNSHARE, 1, 12, 5, "uploading")

        async with session.post(upload_url, headers=headers, json=data, timeout=aiohttp.ClientTimeout(total=300)) as response:
            if response.status in (200, 201):
                result = await response.json()
                print(f"UPNshare upload response: {result}")  # Debug logging

                # Try multiple ways to extract upload ID
                upload_id = (result.get("id") or
                           result.get("data", {}).get("id") or
                           result.get("upload_id") or
                           result.get("file_id") or
                           result.get("video_id"))

                # If no upload ID but response indicates success, try alternative approach
                if not upload_id and result.get("status") == "success":
                    # Generate a fallback ID based on response data
                    upload_id = result.get("message", "").split()[-1] if result.get("message") else None

                if upload_id:
                    await _update_progress(task, Platform.UPNSHARE, 3, 12, 5, "processing")

                    # Step 2: Poll for task completion using correct API flow
                    max_attempts = 60
                    for attempt in range(max_attempts):
                        if cancel_event and cancel_event.is_set():
                            return {"error": "Upload cancelled"}

                        try:
                            # Check task status using correct endpoint
                            status_url = f"{config['base_url']}{config['status_endpoint']}/{upload_id}"
                            async with session.get(status_url, headers=headers) as status_response:
                                if status_response.status == 200:
                                    status_result = await status_response.json()
                                    task_status = status_result.get("status", "").lower()

                                    if task_status in ("completed", "ready", "success"):
                                        # Extract video IDs from completed task
                                        videos = status_result.get("videos", [])
                                        if videos:
                                            # Use first video ID as file code
                                            file_code = videos[0] if isinstance(videos, list) else str(videos)
                                            embed_code = config["embed_template"].format(file_code=file_code)
                                            await _update_progress(task, Platform.UPNSHARE, 12, 12, 5, "completed")
                                            await _save_embed_code(task, Platform.UPNSHARE, embed_code, file_code, task.filename)
                                            return {"file_code": file_code, "embed_code": embed_code, "status": "completed"}
                                        else:
                                            print(f"UPNshare: Task completed but no videos found in response")

                                    elif task_status in ("failed", "error"):
                                        error_msg = status_result.get("error", "Upload failed")
                                        return {"error": f"Upload failed: {error_msg}"}

                        except Exception as e:
                            print(f"UPNshare status check error: {e}")

                        # Update progress
                        current_step = min(3 + (attempt // 3), 11)
                        await _update_progress(task, Platform.UPNSHARE, current_step, 12, 5, "processing")
                        await asyncio.sleep(5)

                # If no upload ID found, return error
                return {"error": "No upload ID received from UPNshare"}

            elif response.status == 429:
                # Rate limiting - set rate limit and add to retry queue
                response_text = await response.text()
                print(f"UPNshare rate limited: {response.status} - {response_text}")

                # Set rate limit for 5 minutes
                await set_rate_limit("upnshare", 300, "HTTP 429 Rate Limited")

                # Add to retry queue
                await add_rate_limited_task("upnshare", task.id, {
                    "filename": task.filename,
                    "url": task.url,
                    "retry_reason": "HTTP 429 Rate Limited"
                })

                return {"error": f"HTTP {response.status}: Rate limited - added to retry queue", "status": "rate_limited"}
            else:
                response_text = await response.text()
                print(f"UPNshare upload failed: {response.status} - {response_text}")
                return {"error": f"HTTP {response.status}: Upload request failed"}

    except Exception as e:
        print(f"UPNshare upload exception: {e}")
        return {"error": f"Exception during upload: {str(e)}"}

# Platform verification functions
async def verify_upload_on_platform(session: aiohttp.ClientSession, platform: Platform, potential_file_codes: list, config: dict, task: UploadTask) -> Dict[str, Any]:
    """Verify if upload actually exists on platform and get real embed code"""

    if platform == Platform.LULUSTREAM:
        return await verify_lulustream_upload(session, potential_file_codes, config, task)
    elif platform == Platform.STREAMP2P:
        return await verify_streamp2p_upload(session, potential_file_codes, config, task)
    elif platform == Platform.RPMSHARE:
        return await verify_rpmshare_upload(session, potential_file_codes, config, task)
    elif platform == Platform.FILEMOON:
        return await verify_filemoon_upload(session, potential_file_codes, config, task)
    elif platform == Platform.UPNSHARE:
        return await verify_upnshare_upload(session, potential_file_codes, config, task)

    return {"error": f"Unknown platform: {platform}"}

async def verify_lulustream_upload(session: aiohttp.ClientSession, potential_file_codes: list, config: dict, task: UploadTask) -> Dict[str, Any]:
    """Verify Lulustream upload by checking file info endpoint"""
    for file_code in potential_file_codes:
        try:
            info_url = f"{config['base_url']}{config['info_endpoint']}"
            params = {"key": config["api_key"], "file_code": file_code}

            async with session.get(info_url, params=params) as response:
                if response.status == 200:
                    result = await response.json()
                    if result.get("status") == 200 and result.get("result"):
                        file_info = result["result"]

                        # Check if file is ready/playable
                        can_play = file_info.get("canplay")
                        status = file_info.get("status")
                        ready = file_info.get("ready")

                        is_ready = (
                            can_play in [1, "1", True] or
                            status in ["ready", "completed", "active", "published"] or
                            ready in [1, "1", True]
                        )

                        if is_ready:
                            embed_code = config["embed_template"].format(file_code=file_code)
                            return {"file_code": file_code, "embed_code": embed_code, "status": "completed"}
        except Exception as e:
            print(f"Error verifying Lulustream file {file_code}: {e}")
            continue

    return {"error": "No valid upload found on Lulustream"}

async def verify_streamp2p_upload(session: aiohttp.ClientSession, potential_file_codes: list, config: dict, task: UploadTask) -> Dict[str, Any]:
    """Verify StreamP2P upload by checking manage endpoint and searching by URL/title"""
    headers = {"api-token": config['api_key'], "Content-Type": "application/json"}

    # First try with potential file codes
    for file_code in potential_file_codes:
        try:
            # Try to get video info using manage endpoint
            manage_url = f"{config['base_url']}{config['info_endpoint']}"
            params = {"video_id": file_code}

            async with session.get(manage_url, headers=headers, params=params) as response:
                if response.status == 200:
                    result = await response.json()
                    if result.get("status") == "success" and result.get("data"):
                        video_data = result["data"]
                        if video_data.get("status") in ["completed", "ready", "active"]:
                            embed_code = config["embed_template"].format(file_code=file_code)
                            return {"file_code": file_code, "embed_code": embed_code, "status": "completed"}
        except Exception as e:
            print(f"Error verifying StreamP2P file {file_code}: {e}")
            continue

    # If file codes don't work, try to search by listing recent uploads
    try:
        list_url = f"{config['base_url']}{config['info_endpoint']}"
        params = {"limit": 50}  # Get recent uploads

        async with session.get(list_url, headers=headers, params=params) as response:
            if response.status == 200:
                result = await response.json()
                if result.get("status") == "success" and result.get("data"):
                    videos = result["data"] if isinstance(result["data"], list) else [result["data"]]

                    # Search for video by URL or filename
                    for video in videos:
                        video_url = video.get("source_url", "")
                        video_title = video.get("title", "")
                        video_id = video.get("id", "")

                        # Check if this matches our upload
                        if (task.url in video_url or
                            task.filename in video_title or
                            any(code in video_url for code in potential_file_codes)):

                            if video.get("status") in ["completed", "ready", "active"]:
                                embed_code = config["embed_template"].format(file_code=video_id)
                                return {"file_code": video_id, "embed_code": embed_code, "status": "completed"}
    except Exception as e:
        print(f"Error searching StreamP2P uploads: {e}")

    return {"error": "No valid upload found on StreamP2P"}

async def verify_rpmshare_upload(session: aiohttp.ClientSession, potential_file_codes: list, config: dict, task: UploadTask) -> Dict[str, Any]:
    """Verify RPMShare upload by checking manage endpoint and searching by URL/title"""
    headers = {"api-token": config['api_key'], "Content-Type": "application/json"}

    # First try with potential file codes
    for file_code in potential_file_codes:
        try:
            # Try to get video info using manage endpoint
            manage_url = f"{config['base_url']}{config['info_endpoint']}"
            params = {"video_id": file_code}

            async with session.get(manage_url, headers=headers, params=params) as response:
                if response.status == 200:
                    result = await response.json()
                    if result.get("status") == "success" and result.get("data"):
                        video_data = result["data"]
                        if video_data.get("status") in ["completed", "ready", "active"]:
                            embed_code = config["embed_template"].format(file_code=file_code)
                            return {"file_code": file_code, "embed_code": embed_code, "status": "completed"}
        except Exception as e:
            print(f"Error verifying RPMShare file {file_code}: {e}")
            continue

    # If file codes don't work, try to search by listing recent uploads
    try:
        list_url = f"{config['base_url']}{config['info_endpoint']}"
        params = {"limit": 50}  # Get recent uploads

        async with session.get(list_url, headers=headers, params=params) as response:
            if response.status == 200:
                result = await response.json()
                if result.get("status") == "success" and result.get("data"):
                    videos = result["data"] if isinstance(result["data"], list) else [result["data"]]

                    # Search for video by URL or filename
                    for video in videos:
                        video_url = video.get("source_url", "")
                        video_title = video.get("title", "")
                        video_id = video.get("id", "")

                        # Check if this matches our upload
                        if (task.url in video_url or
                            task.filename in video_title or
                            any(code in video_url for code in potential_file_codes)):

                            if video.get("status") in ["completed", "ready", "active"]:
                                embed_code = config["embed_template"].format(file_code=video_id)
                                return {"file_code": video_id, "embed_code": embed_code, "status": "completed"}
    except Exception as e:
        print(f"Error searching RPMShare uploads: {e}")

    return {"error": "No valid upload found on RPMShare"}

async def verify_filemoon_upload(session: aiohttp.ClientSession, potential_file_codes: list, config: dict, task: UploadTask) -> Dict[str, Any]:
    """Verify FileMoon upload by checking file info endpoint"""
    for file_code in potential_file_codes:
        try:
            info_url = f"{config['base_url']}{config['info_endpoint']}"
            params = {"key": config["api_key"], "file_code": file_code}

            async with session.get(info_url, params=params) as response:
                if response.status == 200:
                    result = await response.json()
                    if result.get("status") == 200 and result.get("result"):
                        file_info = result["result"]
                        if isinstance(file_info, list) and file_info:
                            file_info = file_info[0]

                        if file_info.get("status") in ["ready", "completed", "active"]:
                            # Get the actual filename for the embed URL
                            filename = file_info.get("name", task.filename)
                            # Remove extension and clean filename for URL
                            clean_filename = filename.rsplit('.', 1)[0] if '.' in filename else filename

                            # FileMoon embed URLs include filename: /e/{file_code}/{filename}
                            # Clean filename for URL (remove special chars, limit length)
                            clean_filename = clean_filename.replace(' ', '_').replace('-', '_')
                            # Limit filename length for better URLs
                            if len(clean_filename) > 80:
                                clean_filename = clean_filename[:80]
                            embed_url = f"https://filemoon.to/e/{file_code}/{clean_filename}"
                            embed_code = f'<iframe src="{embed_url}" frameborder="0" marginwidth="0" marginheight="0" scrolling="no" width="640" height="360" allowfullscreen></iframe>'

                            return {"file_code": file_code, "embed_code": embed_code, "status": "completed"}
        except Exception as e:
            print(f"Error verifying FileMoon file {file_code}: {e}")
            continue

    return {"error": "No valid upload found on FileMoon"}

async def verify_upnshare_upload(session: aiohttp.ClientSession, potential_file_codes: list, config: dict, task: UploadTask) -> Dict[str, Any]:
    """Verify UPNshare upload by checking manage endpoint and searching by URL/title"""
    headers = {"api-token": config['api_key'], "Content-Type": "application/json"}

    # First try with potential file codes
    for file_code in potential_file_codes:
        try:
            # Try to get video info using manage endpoint
            manage_url = f"{config['base_url']}{config['info_endpoint']}"
            params = {"video_id": file_code}

            async with session.get(manage_url, headers=headers, params=params) as response:
                if response.status == 200:
                    result = await response.json()
                    if result.get("status") == "success" and result.get("data"):
                        video_data = result["data"]
                        if video_data.get("status") in ["completed", "ready", "active"]:
                            embed_code = config["embed_template"].format(file_code=file_code)
                            return {"file_code": file_code, "embed_code": embed_code, "status": "completed"}
        except Exception as e:
            print(f"Error verifying UPNshare file {file_code}: {e}")
            continue

    # If file codes don't work, try to search by listing recent uploads
    try:
        list_url = f"{config['base_url']}{config['info_endpoint']}"
        params = {"limit": 50}  # Get recent uploads

        async with session.get(list_url, headers=headers, params=params) as response:
            if response.status == 200:
                result = await response.json()
                if result.get("status") == "success" and result.get("data"):
                    videos = result["data"] if isinstance(result["data"], list) else [result["data"]]

                    # Search for video by URL or filename
                    for video in videos:
                        video_url = video.get("source_url", "")
                        video_title = video.get("title", "")
                        video_id = video.get("id", "")

                        # Check if this matches our upload
                        if (task.url in video_url or
                            task.filename in video_title or
                            any(code in video_url for code in potential_file_codes)):

                            if video.get("status") in ["completed", "ready", "active"]:
                                embed_code = config["embed_template"].format(file_code=video_id)
                                return {"file_code": video_id, "embed_code": embed_code, "status": "completed"}
    except Exception as e:
        print(f"Error searching UPNshare uploads: {e}")

    return {"error": "No valid upload found on UPNshare"}

# Helper functions
async def robust_http_request(session: aiohttp.ClientSession, method: str, url: str, max_retries: int = 3, **kwargs) -> tuple:
    """Make HTTP request with retry logic and better error handling"""
    last_exception = None

    for attempt in range(max_retries):
        try:
            if method.upper() == 'GET':
                async with session.get(url, **kwargs) as response:
                    return response.status, await response.json(), None
            elif method.upper() == 'POST':
                async with session.post(url, **kwargs) as response:
                    return response.status, await response.json(), None
        except aiohttp.ClientConnectionError as e:
            last_exception = e
            print(f"Connection error on attempt {attempt + 1}/{max_retries}: {e}")
            if attempt < max_retries - 1:
                await asyncio.sleep(2 ** attempt)  # Exponential backoff
        except aiohttp.ClientTimeout as e:
            last_exception = e
            print(f"Timeout error on attempt {attempt + 1}/{max_retries}: {e}")
            if attempt < max_retries - 1:
                await asyncio.sleep(2 ** attempt)
        except Exception as e:
            last_exception = e
            print(f"Unexpected error on attempt {attempt + 1}/{max_retries}: {e}")
            if attempt < max_retries - 1:
                await asyncio.sleep(2 ** attempt)

    # All retries failed
    return None, None, last_exception

async def _update_progress(task: UploadTask, platform: Platform, current_step: int, total_steps: int, interval_s: int = 5, stage: str = "processing"):
    """Update progress for a specific platform with enhanced accuracy"""
    stage_ranges = {
        "uploading": (0, 30),      # Initial upload submission
        "processing": (30, 85),    # Platform processing
        "verifying": (85, 95),     # Verification stage
        "completed": (100, 100)    # Completed
    }

    base_min, base_max = stage_ranges.get(stage, (0, 95))

    if stage == "completed":
        percent = 100.0
        eta_seconds = 0
        speed_mbps = 0.0
    else:
        # More accurate progress calculation
        stage_progress = min(current_step / max(total_steps, 1), 1.0)
        percent = base_min + (stage_progress * (base_max - base_min))
        percent = max(0.0, min(percent, 95.0))

        # More realistic ETA calculation
        remaining_steps = max(0, total_steps - current_step)
        base_eta = remaining_steps * interval_s

        # Add stage-specific time adjustments
        if stage == "uploading":
            eta_seconds = base_eta + 10  # Add buffer for upload initiation
            speed_mbps = 3.2  # Realistic upload speed
        elif stage == "processing":
            eta_seconds = base_eta + 30  # Add buffer for platform processing
            speed_mbps = 0.8  # Processing speed
        elif stage == "verifying":
            eta_seconds = base_eta + 15  # Add buffer for verification
            speed_mbps = 1.5  # Verification speed
        else:
            eta_seconds = base_eta
            speed_mbps = 1.0

    # Update task with more accurate values
    task.progress[platform.value] = round(percent, 1)
    task.eta_seconds[platform.value] = max(0, eta_seconds)
    task.speeds[platform.value] = round(speed_mbps, 2)
    task.platform_status[platform.value] = stage

    # Add timestamp for progress tracking
    task.last_updated = datetime.utcnow()

    # Broadcast update via WebSocket
    await broadcast_task_update(task)

async def broadcast_queue_status():
    """Broadcast queue status to all connected clients"""
    try:
        queue_status = {
            "type": "queue_status",
            "data": {
                "total_queued": len(upload_queue),
                "total_active": len(active_uploads),
                "total_completed": len(completed_uploads),
                "total_failed": len(failed_uploads)
            }
        }

        # Broadcast to all connected WebSocket clients
        if active_connections:
            message = json.dumps(queue_status)
            disconnected = []

            for ws in active_connections:
                try:
                    await ws.send_text(message)
                except:
                    disconnected.append(ws)

            # Remove disconnected clients
            for ws in disconnected:
                if ws in active_connections:
                    active_connections.remove(ws)

    except Exception as e:
        print(f"Error broadcasting queue status: {e}")

async def _save_embed_code(task: UploadTask, platform: Platform, embed_code: str, file_code: str, filename: str):
    """Save embed code to task"""
    task.embed_codes[platform.value] = embed_code
    task.file_codes[platform.value] = file_code

    # Update filename if we got a better one
    if filename and filename != task.filename:
        task.filename = filename

    # Save to file
    await save_task_to_file(task)

# Queue management functions
async def start_next_uploads():
    """Start next uploads maintaining concurrency limits with sequential uploads for Lulustream & UpnShare"""
    global lulustream_active_uploads, upnshare_active_uploads
    started_count = 0

    while len(active_uploads) < MAX_CONCURRENT_UPLOADS and upload_queue:
        task = upload_queue.pop(0)
        task.status = UploadStatus.IN_PROGRESS
        task.started_at = datetime.utcnow()

        # Add to active uploads
        active_uploads[task.id] = task
        cancel_flags[task.id] = asyncio.Event()

        # Create platform upload tasks with sequential handling for Lulustream & UpnShare
        platform_tasks = []
        for platform in Platform:
            if platform == Platform.LULUSTREAM:
                # Sequential upload for Lulustream
                if lulustream_active_uploads < MAX_SEQUENTIAL_UPLOADS:
                    lulustream_active_uploads += 1
                    platform_task = asyncio.create_task(
                        upload_to_platform_sequential(platform, task, cancel_flags[task.id])
                    )
                    platform_tasks.append(platform_task)
                    print(f"[SEQUENTIAL] Started Lulustream upload for {task.filename} (Active: {lulustream_active_uploads})")
                else:
                    # Add to Lulustream queue
                    lulustream_queue.append({"task_id": task.id, "task": task})
                    print(f"[SEQUENTIAL] Queued Lulustream upload for {task.filename} (Queue: {len(lulustream_queue)})")
                    # Create a placeholder task that will be handled later
                    platform_task = asyncio.create_task(
                        handle_queued_platform_upload(platform, task, cancel_flags[task.id])
                    )
                    platform_tasks.append(platform_task)
            elif platform == Platform.UPNSHARE:
                # Sequential upload for UpnShare
                if upnshare_active_uploads < MAX_SEQUENTIAL_UPLOADS:
                    upnshare_active_uploads += 1
                    platform_task = asyncio.create_task(
                        upload_to_platform_sequential(platform, task, cancel_flags[task.id])
                    )
                    platform_tasks.append(platform_task)
                    print(f"[SEQUENTIAL] Started UpnShare upload for {task.filename} (Active: {upnshare_active_uploads})")
                else:
                    # Add to UpnShare queue
                    upnshare_queue.append({"task_id": task.id, "task": task})
                    print(f"[SEQUENTIAL] Queued UpnShare upload for {task.filename} (Queue: {len(upnshare_queue)})")
                    # Create a placeholder task that will be handled later
                    platform_task = asyncio.create_task(
                        handle_queued_platform_upload(platform, task, cancel_flags[task.id])
                    )
                    platform_tasks.append(platform_task)
            else:
                # Concurrent upload for other platforms (StreamP2P, RPMShare, FileMoon)
                platform_task = asyncio.create_task(
                    upload_to_platform(platform, task, cancel_flags[task.id])
                )
                platform_tasks.append(platform_task)

        running_platform_tasks[task.id] = platform_tasks

        # Monitor completion
        asyncio.create_task(monitor_upload_completion(task.id, platform_tasks))

        started_count += 1
        print(f"Started upload for task {task.id[:8]} ({task.filename}). Active: {len(active_uploads)}, Queued: {len(upload_queue)}")

    if started_count > 0:
        print(f"Queue management: Started {started_count} new uploads. Current active: {len(active_uploads)}/{MAX_CONCURRENT_UPLOADS}, Remaining in queue: {len(upload_queue)}")
        print(f"Sequential queues - Lulustream: {len(lulustream_queue)}, UpnShare: {len(upnshare_queue)}")
        print(f"Sequential active - Lulustream: {lulustream_active_uploads}/{MAX_SEQUENTIAL_UPLOADS}, UpnShare: {upnshare_active_uploads}/{MAX_SEQUENTIAL_UPLOADS}")

    # Always log current queue status for verification
    total_files_in_system = len(upload_queue) + len(active_uploads) + len(completed_uploads) + len(failed_uploads)
    print(f"[QUEUE-STATUS] Total files in system: {total_files_in_system} | Queue: {len(upload_queue)} | Active: {len(active_uploads)} | Completed: {len(completed_uploads)} | Failed: {len(failed_uploads)}")
    print(f"[QUEUE-STATUS] Sequential - Lulustream queue: {len(lulustream_queue)}, active: {lulustream_active_uploads} | UpnShare queue: {len(upnshare_queue)}, active: {upnshare_active_uploads}")

    # Start next sequential uploads if possible
    await start_next_sequential_uploads()

    # Broadcast queue status update
    await broadcast_queue_status()

async def start_next_sequential_uploads():
    """Start next uploads from sequential queues when capacity is available"""
    global lulustream_active_uploads, upnshare_active_uploads

    # Start next Lulustream upload if available
    if lulustream_active_uploads < MAX_SEQUENTIAL_UPLOADS and lulustream_queue:
        queued_item = lulustream_queue.pop(0)
        task = queued_item["task"]
        lulustream_active_uploads += 1

        # Start the actual upload
        asyncio.create_task(
            upload_to_platform_sequential(Platform.LULUSTREAM, task, cancel_flags.get(task.id))
        )
        print(f"[SEQUENTIAL] Started queued Lulustream upload for {task.filename} (Active: {lulustream_active_uploads})")

    # Start next UpnShare upload if available
    if upnshare_active_uploads < MAX_SEQUENTIAL_UPLOADS and upnshare_queue:
        queued_item = upnshare_queue.pop(0)
        task = queued_item["task"]
        upnshare_active_uploads += 1

        # Start the actual upload
        asyncio.create_task(
            upload_to_platform_sequential(Platform.UPNSHARE, task, cancel_flags.get(task.id))
        )
        print(f"[SEQUENTIAL] Started queued UpnShare upload for {task.filename} (Active: {upnshare_active_uploads})")

async def upload_to_platform_sequential(platform: Platform, task: UploadTask, cancel_event: asyncio.Event):
    """Upload to platform with sequential completion tracking"""
    global lulustream_active_uploads, upnshare_active_uploads

    try:
        # Perform the actual upload
        result = await upload_to_platform(platform, task, cancel_event)
        return result
    finally:
        # Decrement active count and start next upload
        if platform == Platform.LULUSTREAM:
            lulustream_active_uploads = max(0, lulustream_active_uploads - 1)
            print(f"[SEQUENTIAL] Completed Lulustream upload for {task.filename} (Active: {lulustream_active_uploads})")
        elif platform == Platform.UPNSHARE:
            upnshare_active_uploads = max(0, upnshare_active_uploads - 1)
            print(f"[SEQUENTIAL] Completed UpnShare upload for {task.filename} (Active: {upnshare_active_uploads})")

        # Start next sequential upload if available
        await start_next_sequential_uploads()

async def handle_queued_platform_upload(platform: Platform, task: UploadTask, cancel_event: asyncio.Event):
    """Placeholder function for queued uploads - they will be handled by start_next_sequential_uploads"""
    # This is a placeholder that returns immediately
    # The actual upload will be started by start_next_sequential_uploads()
    return {"status": "queued", "message": f"Upload queued for {platform.value}"}

async def upload_to_platform(platform: Platform, task: UploadTask, cancel_event: asyncio.Event):
    """Route upload to appropriate platform function with duplicate prevention and rate limiting"""
    config = PLATFORM_CONFIGS[platform]

    if not config["api_key"]:
        return {"error": f"{platform.value} API key not configured"}

    # STEP 0: Check rate limiting
    rate_check = await check_rate_limit(platform.value)
    if rate_check["is_limited"]:
        wait_time = int(rate_check["wait_time"])
        print(f"[RATE-LIMIT] {platform.value} is rate limited, adding to retry queue (wait: {wait_time}s)")

        # Update task status to show rate limiting
        task.platform_status[platform.value] = f"rate_limited_retry_{wait_time}s"
        await _update_progress(task, platform, 0, 10, 0, f"rate_limited")

        # Add to retry queue instead of waiting
        await add_rate_limited_task(platform.value, task.id, {
            "filename": task.filename,
            "url": task.url,
            "retry_reason": rate_check["reason"]
        })

        # Broadcast rate limit info to frontend
        await broadcast_task_update(task)

        # Return rate limited status instead of waiting
        return {"error": f"Rate limited - added to retry queue (will retry in {wait_time}s)", "status": "rate_limited"}

    try:
        async with aiohttp.ClientSession() as session:
            # STEP 1: Check if file already exists on this platform
            existing_check = await check_platform_for_existing_file(session, platform, task, config)

            if existing_check.get("exists"):
                print(f"[SKIP] {platform.value}: File already exists - {existing_check.get('file_code', 'unknown')}")

                # Mark as completed and save existing embed code
                task.platform_status[platform.value] = "completed"
                if existing_check.get("embed_code"):
                    task.embed_codes[platform.value] = existing_check["embed_code"]
                if existing_check.get("file_code"):
                    task.file_codes[platform.value] = existing_check["file_code"]

                await _update_progress(task, platform, 10, 10, 0, "completed")

                return {
                    "file_code": existing_check.get("file_code", ""),
                    "embed_code": existing_check.get("embed_code", ""),
                    "status": "completed",
                    "skipped": True,
                    "reason": f"File already exists on {platform.value}"
                }

            # STEP 2: Check if upload was cancelled before starting
            if cancel_event and cancel_event.is_set():
                return {"error": "Upload cancelled before starting"}

            # STEP 3: Proceed with actual upload
            print(f"[UPLOAD] {platform.value}: {task.filename}")

            if platform == Platform.LULUSTREAM:
                result = await upload_to_lulustream(session, task, config, cancel_event)
            elif platform == Platform.STREAMP2P:
                result = await upload_to_streamp2p(session, task, config, cancel_event)
            elif platform == Platform.RPMSHARE:
                result = await upload_to_rpmshare(session, task, config, cancel_event)
            elif platform == Platform.FILEMOON:
                result = await upload_to_filemoon(session, task, config, cancel_event)
            elif platform == Platform.UPNSHARE:
                result = await upload_to_upnshare(session, task, config, cancel_event)
            else:
                result = {"error": f"Unknown platform: {platform}"}

            # Handle result
            if "error" in result:
                task.error_messages[platform.value] = result["error"]
                task.platform_status[platform.value] = "failed"
                print(f"[FAILED] {platform.value}: {result['error']}")
            else:
                task.platform_status[platform.value] = "completed"
                print(f"[SUCCESS] {platform.value}: {result.get('file_code', 'unknown')}")

            return result

    except Exception as e:
        error_msg = f"Exception in {platform.value}: {str(e)}"
        task.error_messages[platform.value] = error_msg
        task.platform_status[platform.value] = "failed"
        print(f"[EXCEPTION] {platform.value}: {error_msg}")
        return {"error": error_msg}

async def monitor_upload_completion(task_id: str, platform_tasks: List[asyncio.Task]):
    """Monitor when all platform uploads are complete for a task"""
    try:
        # Wait for all platform tasks to complete
        await asyncio.gather(*platform_tasks, return_exceptions=True)

        # Move task from active to completed
        if task_id in active_uploads:
            task = active_uploads.pop(task_id)
            task.completed_at = datetime.utcnow()

            # Check if all platforms completed successfully
            completed_platforms = sum(1 for status in task.platform_status.values() if status == "completed")
            total_platforms = len(Platform)

            print(f"Task {task_id[:8]} completed: {completed_platforms}/{total_platforms} platforms successful")

            if completed_platforms == total_platforms:
                task.status = UploadStatus.COMPLETED
                completed_uploads[task_id] = task
                print(f"[COMPLETE] Task {task_id[:8]} ({task.filename}) - ALL platforms successful")
            elif completed_platforms > 0:
                task.status = UploadStatus.COMPLETED  # Partial success still counts as completed
                completed_uploads[task_id] = task
                print(f"[PARTIAL] Task {task_id[:8]} ({task.filename}) - Partial success: {completed_platforms}/{total_platforms}")
            else:
                task.status = UploadStatus.FAILED
                failed_uploads[task_id] = task
                print(f"[FAILED] Task {task_id[:8]} ({task.filename}) - ALL platforms failed")

            # Clean up
            if task_id in cancel_flags:
                del cancel_flags[task_id]
            if task_id in running_platform_tasks:
                del running_platform_tasks[task_id]

            # Save to file
            await save_task_to_file(task)

            # Broadcast final update
            await broadcast_task_update(task)

            # Start next uploads
            await start_next_uploads()

    except Exception as e:
        print(f"Error monitoring upload completion for task {task_id}: {e}")

# CSV monitoring functions removed - no auto CSV generation

# CSV notification functions removed

    except Exception as e:
        print(f"Error broadcasting CSV available notification: {e}")

async def broadcast_dmca_notification(task: UploadTask, file_code: str, dmca_message: str):
    """Broadcast DMCA false positive detection"""
    try:
        notification = {
            "type": "dmca_detected",
            "data": {
                "task_id": task.id,
                "filename": task.filename,
                "file_code": file_code,
                "dmca_message": dmca_message,
                "platform": "lulustream",
                "timestamp": datetime.utcnow().isoformat()
            }
        }

        message = json.dumps(notification, default=str)
        disconnected = []

        for ws in active_connections:
            try:
                await ws.send_text(message)
            except:
                disconnected.append(ws)

        # Remove disconnected clients
        for ws in disconnected:
            if ws in active_connections:
                active_connections.remove(ws)

        print(f"[DMCA-BROADCAST] Notified frontend about DMCA detection: {task.filename}")

    except Exception as e:
        print(f"Error broadcasting DMCA notification: {e}")

# CSV Generation
async def generate_csv_file(session_id: str = None) -> str:
    """Generate CSV file with available embed codes (real-time)"""
    # Get tasks with available embed codes from multiple sources
    tasks_to_export = []

    # PRIORITY 1: Tasks with all embed codes ready (from CSV monitoring)
    if session_id:
        csv_ready_filtered = [task for task in csv_ready_tasks.values() if task.session_id == session_id]
    else:
        csv_ready_filtered = list(csv_ready_tasks.values())

    # PRIORITY 2: Completed tasks from memory
    if session_id:
        memory_tasks = [task for task in completed_uploads.values() if task.session_id == session_id]
    else:
        memory_tasks = list(completed_uploads.values())

    # PRIORITY 3: Active tasks with partial embed codes
    if session_id:
        active_with_embeds = [task for task in active_uploads.values()
                             if task.session_id == session_id and any(task.embed_codes.values())]
    else:
        active_with_embeds = [task for task in active_uploads.values()
                             if any(task.embed_codes.values())]

    # PRIORITY 4: Load from file storage for historical tasks with embed codes
    try:
        file_tasks_data = await load_tasks_from_file()
        file_tasks = []

        for task_data in file_tasks_data:
            # Include any tasks with embed codes (not just completed)
            if (task_data.get('embed_codes') and
                any(task_data.get('embed_codes', {}).values())):

                # Filter by session_id if provided
                if not session_id or task_data.get('session_id') == session_id:
                    # Convert dict to UploadTask object for consistency
                    try:
                        # Handle datetime conversion
                        for key in ['created_at', 'started_at', 'completed_at', 'last_updated']:
                            if key in task_data and isinstance(task_data[key], str):
                                task_data[key] = datetime.fromisoformat(task_data[key].replace('Z', '+00:00'))

                        task = UploadTask(**task_data)
                        file_tasks.append(task)
                    except Exception as e:
                        print(f"Error converting task data: {e}")
                        continue

        # Combine all sources, removing duplicates by ID (priority order maintained)
        all_tasks = {}

        # Add in priority order
        for task in csv_ready_filtered:
            all_tasks[task.id] = task
        for task in memory_tasks:
            if task.id not in all_tasks:
                all_tasks[task.id] = task
        for task in active_with_embeds:
            if task.id not in all_tasks:
                all_tasks[task.id] = task
        for task in file_tasks:
            if task.id not in all_tasks:
                all_tasks[task.id] = task

        tasks_to_export = list(all_tasks.values())

    except Exception as e:
        print(f"Error loading tasks from file for CSV: {e}")
        # Fallback to available tasks
        all_tasks = {}
        for task in csv_ready_filtered + memory_tasks + active_with_embeds:
            all_tasks[task.id] = task
        tasks_to_export = list(all_tasks.values())

    if not tasks_to_export:
        print(f"[CSV-DEBUG] No tasks found for export. Session ID: {session_id}")
        print(f"[CSV-DEBUG] CSV ready tasks: {len(csv_ready_tasks)}")
        print(f"[CSV-DEBUG] Memory tasks: {len(memory_tasks)}")
        print(f"[CSV-DEBUG] Active with embeds: {len(active_with_embeds)}")
        raise HTTPException(status_code=404, detail="No uploads with embed codes found")

    print(f"[CSV-DEBUG] Found {len(tasks_to_export)} tasks to export for session {session_id}")

    # Create CSV content
    timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
    filename = f"embed_codes_{timestamp}.csv"
    # Use backend directory instead of /tmp/ for Windows compatibility
    backend_dir = os.path.dirname(os.path.abspath(__file__))
    filepath = os.path.join(backend_dir, "data", filename)

    async with aiofiles.open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
        # Create CSV writer manually since aiofiles doesn't support csv module directly
        header = "Filename,Embed Codes\n"
        await csvfile.write(header)

        for task in tasks_to_export:
            filename_clean = task.filename.replace('"', '""')  # Escape quotes

            # Create embed codes in specified order with line breaks
            embed_lines = []

            # 1st line - Lulustream embed codes
            embed_lines.append(task.embed_codes.get('lulustream', ''))
            # 2nd line - Streamp2p embed codes
            embed_lines.append(task.embed_codes.get('streamp2p', ''))
            # 3rd line - Rpmshare embed codes
            embed_lines.append(task.embed_codes.get('rpmshare', ''))
            # 4th line - Filemoon embed codes
            embed_lines.append(task.embed_codes.get('filemoon', ''))
            # 5th line - Upnshare embed codes
            embed_lines.append(task.embed_codes.get('upnshare', ''))

            # Join with double line breaks as specified
            embed_string = '\n\n'.join(embed_lines) if any(embed_lines) else "No embed codes available"
            embed_string_escaped = embed_string.replace('"', '""')  # Escape quotes

            # Write row
            row = f'"{filename_clean}","{embed_string_escaped}"\n'
            await csvfile.write(row)

    return filepath

# WebSocket endpoint
@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    await websocket.accept()
    active_connections.append(websocket)

    try:
        # Send current state
        current_state = {
            "type": "initial_state",
            "data": {
                "queue": [task.dict() for task in upload_queue],
                "active": [task.dict() for task in active_uploads.values()],
                "completed": [task.dict() for task in completed_uploads.values()],
                "failed": [task.dict() for task in failed_uploads.values()]
            }
        }
        await websocket.send_text(json.dumps(current_state, default=str))

        # Keep connection alive
        while True:
            try:
                # Receive ping/pong messages
                data = await websocket.receive_text()
                message = json.loads(data)

                if message.get("type") == "ping":
                    await websocket.send_text(json.dumps({"type": "pong"}))

            except WebSocketDisconnect:
                break

    except Exception as e:
        print(f"WebSocket error: {e}")
    finally:
        if websocket in active_connections:
            active_connections.remove(websocket)

# API Endpoints
@app.post("/api/upload/submit", response_model=Dict[str, Any])
async def submit_urls(request: UrlSubmissionRequest):
    """Submit URLs for upload to all platforms with duplicate detection"""
    try:
        if len(request.urls) > MAX_QUEUE_SIZE:
            raise HTTPException(status_code=400, detail=f"Maximum {MAX_QUEUE_SIZE} URLs allowed")

        if len(upload_queue) + len(request.urls) > MAX_QUEUE_SIZE:
            raise HTTPException(status_code=400, detail=f"Queue would exceed maximum size of {MAX_QUEUE_SIZE}")

        session_id = request.session_id or str(uuid.uuid4())
        created_tasks = []
        duplicate_warnings = []
        url_deduplication = []

        # Check for URL duplicates within the current request
        seen_urls = set()
        unique_urls = []
        for url in request.urls:
            if url in seen_urls:
                url_deduplication.append(url)
                continue
            seen_urls.add(url)
            unique_urls.append(url)

        if url_deduplication:
            print(f"[DEDUP] Removed {len(url_deduplication)} duplicate URLs from current request")

        for url in unique_urls:
            # Extract filename from URL
            filename = url.split('/')[-1].split('?')[0] or f"video_{len(created_tasks)+1}"
            if not filename.endswith(('.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm')):
                filename += ".mp4"  # Default extension

            # Create temporary task for duplicate checking
            temp_task = UploadTask(
                url=url,
                filename=filename,
                session_id=session_id
            )

            # Check for duplicates BEFORE adding to queue
            duplicate_check = await check_for_duplicates(temp_task)

            if duplicate_check["has_duplicates"]:
                # Found duplicates - create warning but don't queue
                duplicate_warnings.append({
                    "url": url,
                    "filename": filename,
                    "duplicates": duplicate_check["duplicates"],
                    "platforms_affected": duplicate_check["platforms_affected"],
                    "total_duplicates": duplicate_check["total_duplicates"]
                })
                print(f"[DUPLICATE] {filename} already exists on {len(duplicate_check['platforms_affected'])} platforms")
                continue

            # Check for resumable uploads
            resume_check = await get_resumable_task_data(temp_task)
            if resume_check["can_resume"]:
                print(f"[RESUMABLE] {filename} - {resume_check['reason']}")
                # Don't create new task, the existing one will be handled
                continue

            # No duplicates found, safe to queue
            upload_queue.append(temp_task)
            created_tasks.append(temp_task)

            # Save to file
            await save_task_to_file(temp_task)
            print(f"[QUEUED] {filename} - No duplicates detected")

        # Start uploads if there's capacity
        await start_next_uploads()

        response = {
            "message": f"Successfully queued {len(created_tasks)} URLs",
            "session_id": session_id,
            "tasks": [task.dict() for task in created_tasks],
            "queued_count": len(created_tasks),
            "duplicate_count": len(duplicate_warnings),
            "url_deduplication_count": len(url_deduplication),
            "total_submitted": len(request.urls),
            "total_processed": len(unique_urls)
        }

        # Add duplicate warnings if any found
        if duplicate_warnings:
            response["duplicate_warnings"] = duplicate_warnings
            response["message"] += f" ({len(duplicate_warnings)} duplicates blocked)"

        # Add URL deduplication info
        if url_deduplication:
            response["url_duplicates_removed"] = url_deduplication
            response["message"] += f" ({len(url_deduplication)} URL duplicates removed)"

        return response

    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        print(f"Error in submit_urls: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@app.post("/api/upload/check-duplicates")
async def check_duplicates_endpoint(request: UrlSubmissionRequest):
    """Check for duplicates without queuing uploads"""
    duplicate_results = []

    for url in request.urls:
        # Extract filename from URL
        filename = url.split('/')[-1].split('?')[0] or f"video_{len(duplicate_results)+1}"
        if not filename.endswith(('.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm')):
            filename += ".mp4"

        # Create temporary task for checking
        temp_task = UploadTask(
            url=url,
            filename=filename,
            session_id="duplicate_check"
        )

        # Check for duplicates
        duplicate_check = await check_for_duplicates(temp_task)
        resume_check = await get_resumable_task_data(temp_task)

        duplicate_results.append({
            "url": url,
            "filename": filename,
            "has_duplicates": duplicate_check["has_duplicates"],
            "duplicates": duplicate_check["duplicates"],
            "platforms_affected": duplicate_check["platforms_affected"],
            "total_duplicates": duplicate_check["total_duplicates"],
            "can_resume": resume_check["can_resume"],
            "resume_info": resume_check if resume_check["can_resume"] else None
        })

    return {
        "results": duplicate_results,
        "total_checked": len(request.urls),
        "duplicates_found": sum(1 for r in duplicate_results if r["has_duplicates"]),
        "resumable_found": sum(1 for r in duplicate_results if r["can_resume"])
    }

@app.get("/api/queue/status", response_model=QueueStatusResponse)
async def get_queue_status():
    """Get current queue status"""
    return QueueStatusResponse(
        total_queued=len(upload_queue),
        total_in_progress=len(active_uploads),
        total_completed=len(completed_uploads),
        total_failed=len(failed_uploads),
        current_queue=upload_queue[:10],  # Show first 10 in queue
        active_uploads=list(active_uploads.values()),
        completed_uploads=list(completed_uploads.values())[-20:]  # Show last 20 completed
    )

@app.delete("/api/upload/cancel/{task_id}")
async def cancel_upload(task_id: str):
    """Cancel a specific upload"""
    if task_id in cancel_flags:
        cancel_flags[task_id].set()

        # Remove from active uploads
        if task_id in active_uploads:
            task = active_uploads.pop(task_id)
            task.status = UploadStatus.CANCELLED
            failed_uploads[task_id] = task

            # Clean up
            if task_id in running_platform_tasks:
                tasks = running_platform_tasks.pop(task_id)
                for t in tasks:
                    t.cancel()

        # Start next upload
        await start_next_uploads()

        return {"message": "Upload cancelled"}
    else:
        raise HTTPException(status_code=404, detail="Upload not found")

@app.get("/api/download/csv")
async def download_csv(session_id: str = None):
    """Download CSV file with embed codes"""
    try:
        filepath = await generate_csv_file(session_id)
        filename = os.path.basename(filepath)

        return FileResponse(
            path=filepath,
            filename=filename,
            media_type='text/csv',
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to generate CSV: {str(e)}")

@app.get("/api/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "queue_size": len(upload_queue),
        "active_uploads": len(active_uploads),
        "completed_uploads": len(completed_uploads),
        "storage_type": "file_based",
        "data_directory": DATA_DIR,
        "tasks_file_exists": os.path.exists(TASKS_FILE)
    }

@app.get("/api/platforms/status")
async def get_platform_status():
    """Get status of all platforms (API keys configured and rate limiting)"""
    status = {}
    current_time = time.time()

    for platform, config in PLATFORM_CONFIGS.items():
        platform_name = platform.value

        # Check rate limiting status
        rate_limit_info = {}
        if platform_name in platform_rate_limits:
            rate_info = platform_rate_limits[platform_name]
            if current_time < rate_info.get("retry_after", 0):
                wait_time = int(rate_info["retry_after"] - current_time)
                rate_limit_info = {
                    "is_limited": True,
                    "wait_time": wait_time,
                    "reason": rate_info.get("reason", "Rate limited"),
                    "retry_after": rate_info["retry_after"]
                }
            else:
                # Rate limit expired, clean it up
                await clear_rate_limit(platform_name)
                rate_limit_info = {"is_limited": False}
        else:
            rate_limit_info = {"is_limited": False}

        status[platform_name] = {
            "configured": bool(config["api_key"]),
            "base_url": config["base_url"],
            "rate_limit": rate_limit_info,
            "retry_queue_count": len(rate_limited_tasks.get(platform_name, []))
        }
    return status

@app.get("/api/retry-queue/status")
async def get_retry_queue_status():
    """Get detailed retry queue status for all platforms"""
    current_time = time.time()
    retry_status = {}

    for platform in Platform:
        platform_name = platform.value

        # Get rate limit info
        rate_limit_info = {}
        if platform_name in platform_rate_limits:
            rate_info = platform_rate_limits[platform_name]
            if current_time < rate_info.get("retry_after", 0):
                wait_time = int(rate_info["retry_after"] - current_time)
                rate_limit_info = {
                    "is_limited": True,
                    "wait_time": wait_time,
                    "reason": rate_info.get("reason", "Rate limited"),
                    "retry_after": rate_info["retry_after"]
                }
            else:
                rate_limit_info = {"is_limited": False}
        else:
            rate_limit_info = {"is_limited": False}

        # Get retry queue info
        retry_queue = rate_limited_tasks.get(platform_name, [])
        retry_queue_info = {
            "count": len(retry_queue),
            "tasks": [
                {
                    "task_id": task["task_id"][:8],
                    "filename": task.get("filename", "Unknown"),
                    "added_at": task.get("added_at", 0),
                    "retry_reason": task.get("retry_reason", "Unknown")
                }
                for task in retry_queue
            ]
        }

        retry_status[platform_name] = {
            "rate_limit": rate_limit_info,
            "retry_queue": retry_queue_info
        }

    return retry_status

@app.delete("/api/retry-queue/clear")
async def clear_retry_queue():
    """Clear completed and failed tasks from retry queues"""
    global rate_limited_tasks, retry_attempts

    cleared_count = 0

    # Clear completed and failed tasks from retry queues
    for platform in list(rate_limited_tasks.keys()):
        original_count = len(rate_limited_tasks[platform])

        # Keep only tasks that are still active or in progress
        active_retry_tasks = []
        for retry_task in rate_limited_tasks[platform]:
            task_id = retry_task["task_id"]

            # Keep task if it's still active or if the platform is still rate limited
            if (task_id in active_uploads or
                platform in platform_rate_limits):
                active_retry_tasks.append(retry_task)
            else:
                cleared_count += 1

        rate_limited_tasks[platform] = active_retry_tasks

        # Clean up empty platform queues
        if not rate_limited_tasks[platform]:
            del rate_limited_tasks[platform]

    # Clean up retry attempts for completed/failed tasks
    for task_id in list(retry_attempts.keys()):
        if task_id not in active_uploads:
            del retry_attempts[task_id]

    print(f"[RETRY-QUEUE-CLEAR] Cleared {cleared_count} completed/failed retry tasks")

    return {
        "message": f"Cleared {cleared_count} completed/failed retry tasks",
        "remaining_queues": {platform: len(tasks) for platform, tasks in rate_limited_tasks.items()}
    }

# Clear completed uploads endpoint
@app.delete("/api/uploads/clear-completed")
async def clear_completed_uploads():
    """Clear completed uploads from memory and persistent storage, keeping last 5 completed"""
    global completed_uploads, failed_uploads

    # Sort completed uploads by completion time (newest first)
    completed_list = list(completed_uploads.values())
    completed_list.sort(key=lambda x: x.completed_at or datetime.min, reverse=True)

    # Keep last 5 completed, clear the rest
    keep_completed = completed_list[:5]
    clear_completed = completed_list[5:]

    # Get IDs to remove
    clear_completed_ids = {task.id for task in clear_completed}
    failed_ids = set(failed_uploads.keys())
    all_clear_ids = clear_completed_ids.union(failed_ids)

    # Update memory - keep last 5 completed, clear all failed
    completed_uploads = {task.id: task for task in keep_completed}
    failed_uploads.clear()

    # Also update persistent storage
    try:
        file_tasks_data = await load_tasks_from_file()
        remaining_tasks = []
        removed_count = 0

        # Keep last 5 completed tasks in storage too
        completed_storage_tasks = []
        for t in file_tasks_data:
            tid = t.get('id')
            tstatus = t.get('status')

            if tstatus == 'completed':
                completed_storage_tasks.append(t)
            elif tid in all_clear_ids or tstatus in ['failed', 'cancelled']:
                removed_count += 1
                continue
            else:
                remaining_tasks.append(t)

        # Sort completed storage tasks by completion time and keep last 5
        completed_storage_tasks.sort(key=lambda x: x.get('completed_at', ''), reverse=True)
        keep_storage_completed = completed_storage_tasks[:5]
        removed_count += len(completed_storage_tasks) - len(keep_storage_completed)

        # Combine remaining tasks with kept completed tasks
        remaining_tasks.extend(keep_storage_completed)

        # Save the filtered tasks back to file
        await save_tasks_to_file(remaining_tasks)

        print(f"[CLEAR] Removed {removed_count} old completed/failed tasks, kept {len(keep_completed)} recent completed tasks")
    except Exception as e:
        print(f"[CLEAR] Error updating persistent storage: {e}")

    return {
        "message": f"Cleared {len(all_clear_ids)} old uploads, kept {len(keep_completed)} recent completed tasks",
        "kept_completed": len(keep_completed),
        "cleared_total": len(all_clear_ids)
    }

# Manual verification endpoint
@app.post("/api/uploads/verify/{task_id}")
async def verify_upload_manually(task_id: str):
    """Manually verify an upload by checking ONLY FAILED platforms (prevents duplicate uploads)"""
    try:
        # Find the task
        task = None
        if task_id in completed_uploads:
            task = completed_uploads[task_id]
        elif task_id in failed_uploads:
            task = failed_uploads[task_id]
        elif task_id in active_uploads:
            task = active_uploads[task_id]
        else:
            # Try to load from file
            file_tasks_data = await load_tasks_from_file()
            for task_data in file_tasks_data:
                if task_data.get('id') == task_id:
                    # Convert to UploadTask object
                    for key in ['created_at', 'started_at', 'completed_at', 'last_updated']:
                        if key in task_data and isinstance(task_data[key], str):
                            task_data[key] = datetime.fromisoformat(task_data[key].replace('Z', '+00:00'))
                    task = UploadTask(**task_data)
                    break

        if not task:
            raise HTTPException(status_code=404, detail="Task not found")

        verification_results = {}
        re_upload_results = {}

        # Count successful vs failed platforms
        successful_platforms = []
        failed_platforms = []

        for platform in Platform:
            current_status = task.platform_status.get(platform.value, "")
            has_embed_code = bool(task.embed_codes.get(platform.value))

            if current_status == "completed" and has_embed_code:
                successful_platforms.append(platform.value)
            else:
                failed_platforms.append(platform.value)

        print(f"[VERIFY] Task {task_id[:8]}: {len(successful_platforms)} successful, {len(failed_platforms)} failed platforms")
        print(f"[VERIFY] Will only retry failed platforms: {failed_platforms}")

        async with aiohttp.ClientSession() as session:
            for platform in Platform:
                config = PLATFORM_CONFIGS[platform]
                if not config["api_key"]:
                    verification_results[platform.value] = {"error": "API key not configured"}
                    continue

                # Check current platform status with enhanced validation
                current_status = task.platform_status.get(platform.value, "")
                has_embed_code = bool(task.embed_codes.get(platform.value))
                has_file_code = bool(task.file_codes.get(platform.value))

                # Enhanced completion check - must have status AND embed code AND file code
                is_truly_completed = (
                    current_status == "completed" and
                    has_embed_code and
                    has_file_code and
                    len(task.embed_codes[platform.value].strip()) > 0 and
                    len(task.file_codes[platform.value].strip()) > 0
                )

                print(f"[VERIFY-DEBUG] {platform.value}: status='{current_status}', embed_code={has_embed_code}, file_code={has_file_code}, truly_completed={is_truly_completed}")

                if is_truly_completed:
                    # Platform already successful - SKIP to prevent duplicate uploads
                    verification_results[platform.value] = {
                        "status": "skipped",
                        "reason": "Already completed successfully - preventing duplicate upload",
                        "embed_code": task.embed_codes[platform.value],
                        "file_code": task.file_codes.get(platform.value, "")
                    }
                    print(f"[VERIFY-SKIP] {platform.value}: Already completed - {task.file_codes.get(platform.value, 'unknown')}")
                    continue

                print(f"[VERIFY-CHECK] {platform.value}: Checking for existing upload...")

                # STEP 1: Use proper platform-specific existing upload check
                if platform == Platform.LULUSTREAM:
                    result = await check_lulustream_existing_upload(session, task, config)
                elif platform == Platform.STREAMP2P:
                    result = await check_streamp2p_existing_task(session, task, config)
                elif platform == Platform.RPMSHARE:
                    result = await check_rpmshare_existing_task(session, task, config)
                elif platform == Platform.FILEMOON:
                    result = await check_filemoon_existing_upload(session, task, config)
                elif platform == Platform.UPNSHARE:
                    result = await check_upnshare_existing_task(session, task, config)
                else:
                    result = None

                if result and result.get("status") == "completed":
                    # Found existing upload
                    task.embed_codes[platform.value] = result["embed_code"]
                    task.file_codes[platform.value] = result["file_code"]
                    task.platform_status[platform.value] = "completed"
                    verification_results[platform.value] = result
                    print(f"[VERIFY-FOUND] {platform.value}: Found existing upload - {result['file_code']}")

                    # Remove any error messages
                    if platform.value in task.error_messages:
                        del task.error_messages[platform.value]
                else:
                    # STEP 2: No existing upload found, need to re-upload
                    print(f"[VERIFY-REUPLOAD] {platform.value}: No existing upload found, starting re-upload...")

                    # Create a cancel event for this re-upload
                    cancel_event = asyncio.Event()

                    # Re-upload to this platform only
                    if platform == Platform.LULUSTREAM:
                        upload_result = await upload_to_lulustream(session, task, config, cancel_event)
                    elif platform == Platform.STREAMP2P:
                        upload_result = await upload_to_streamp2p(session, task, config, cancel_event)
                    elif platform == Platform.RPMSHARE:
                        upload_result = await upload_to_rpmshare(session, task, config, cancel_event)
                    elif platform == Platform.FILEMOON:
                        upload_result = await upload_to_filemoon(session, task, config, cancel_event)
                    elif platform == Platform.UPNSHARE:
                        upload_result = await upload_to_upnshare(session, task, config, cancel_event)
                    else:
                        upload_result = {"error": f"Unknown platform: {platform}"}

                    re_upload_results[platform.value] = upload_result

                    if "error" in upload_result:
                        task.error_messages[platform.value] = upload_result["error"]
                        task.platform_status[platform.value] = "failed"
                        verification_results[platform.value] = {"status": "failed", "error": upload_result["error"]}
                        print(f"[VERIFY-FAILED] {platform.value}: Re-upload failed - {upload_result['error']}")
                    else:
                        task.embed_codes[platform.value] = upload_result.get("embed_code", "")
                        task.file_codes[platform.value] = upload_result.get("file_code", "")
                        task.platform_status[platform.value] = "completed"
                        verification_results[platform.value] = {"status": "completed", **upload_result}
                        print(f"[VERIFY-SUCCESS] {platform.value}: Re-upload successful - {upload_result.get('file_code', 'unknown')}")

                        # Remove any error messages
                        if platform.value in task.error_messages:
                            del task.error_messages[platform.value]

        # Update task status based on verification results
        completed_platforms = sum(1 for status in task.platform_status.values() if status == "completed")
        total_platforms = len(Platform)

        if completed_platforms == total_platforms:
            task.status = UploadStatus.COMPLETED
            completed_uploads[task_id] = task
            # Remove from failed if it was there
            if task_id in failed_uploads:
                del failed_uploads[task_id]
        elif completed_platforms > 0:
            task.status = UploadStatus.COMPLETED  # Partial success
            completed_uploads[task_id] = task
            if task_id in failed_uploads:
                del failed_uploads[task_id]

        # Save updated task
        await save_task_to_file(task)

        return {
            "task_id": task_id,
            "verification_results": verification_results,
            "re_upload_results": re_upload_results,
            "updated_status": task.status,
            "completed_platforms": completed_platforms,
            "total_platforms": total_platforms,
            "skipped_platforms": sum(1 for r in verification_results.values() if r.get("status") == "skipped"),
            "re_uploaded_platforms": len(re_upload_results)
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Verification failed: {str(e)}")

# Clear entire queue endpoint
@app.delete("/api/queue/clear")
async def clear_entire_queue():
    """Clear entire queue including active uploads"""
    global upload_queue, active_uploads, completed_uploads, failed_uploads, cancel_flags, running_platform_tasks

    # Cancel all active uploads
    for task_id in list(active_uploads.keys()):
        if task_id in cancel_flags:
            cancel_flags[task_id].set()

        if task_id in running_platform_tasks:
            tasks = running_platform_tasks[task_id]
            for task in tasks:
                task.cancel()

    # Clear all queues
    upload_queue.clear()
    active_uploads.clear()
    completed_uploads.clear()
    failed_uploads.clear()
    cancel_flags.clear()
    running_platform_tasks.clear()

    return {"message": "Entire queue cleared successfully"}

# End all tasks endpoint
@app.post("/api/uploads/end-all")
async def end_all_tasks():
    """End all active uploads and clear queue"""
    global upload_queue, active_uploads, cancel_flags, running_platform_tasks

    # Cancel all active uploads
    cancelled_count = 0
    for task_id in list(active_uploads.keys()):
        if task_id in cancel_flags:
            cancel_flags[task_id].set()
            cancelled_count += 1

        if task_id in running_platform_tasks:
            tasks = running_platform_tasks[task_id]
            for task in tasks:
                task.cancel()

        # Move to failed uploads
        if task_id in active_uploads:
            task = active_uploads.pop(task_id)
            task.status = UploadStatus.CANCELLED
            failed_uploads[task_id] = task

    # Clear upload queue
    queued_count = len(upload_queue)
    upload_queue.clear()

    return {
        "message": f"Ended {cancelled_count} active uploads and cleared {queued_count} queued items",
        "cancelled_uploads": cancelled_count,
        "cleared_queue": queued_count
    }

# Duplicate File Checker Endpoints

@app.get("/api/duplicates/check")
async def check_duplicate_files():
    """
    Check for duplicate files across all platforms in the last 48 hours (IST timezone)
    """
    try:
        # Import timezone for IST conversion
        from datetime import timezone, timedelta as td

        # Define IST timezone (UTC+5:30)
        IST = timezone(td(hours=5, minutes=30))

        # Get current IST time
        current_ist = datetime.now(IST)
        # Calculate cutoff time (last 48 hours from now in IST)
        cutoff_time_ist = current_ist - timedelta(hours=48)
        # Convert to UTC for API calls (most APIs expect UTC)
        cutoff_time_utc = cutoff_time_ist.astimezone(timezone.utc)
        time_filter = cutoff_time_utc.strftime("%Y-%m-%d %H:%M:%S")
        print(f"[DUPLICATE-CHECK] Last 48 Hours filter: From IST {cutoff_time_ist.strftime('%Y-%m-%d %H:%M:%S')} to {current_ist.strftime('%Y-%m-%d %H:%M:%S')} (UTC cutoff: {time_filter})")

        duplicates = {}

        # Check each platform for files
        for platform in Platform:
            try:
                print(f"[DUPLICATE-CHECK] Checking {platform.value} for duplicate files...")
                if platform == Platform.LULUSTREAM:
                    platform_files = await get_lulustream_files(time_filter, None)
                elif platform == Platform.FILEMOON:
                    platform_files = await get_filemoon_files(time_filter, None)
                elif platform == Platform.STREAMP2P:
                    platform_files = await get_streamp2p_files(time_filter, None)
                elif platform == Platform.RPMSHARE:
                    platform_files = await get_rpmshare_files(time_filter, None)
                elif platform == Platform.UPNSHARE:
                    platform_files = await get_upnshare_files(time_filter, None)
                else:
                    platform_files = []

                print(f"[DUPLICATE-CHECK] {platform.value}: Found {len(platform_files)} files")

                # Find duplicates within this platform
                platform_duplicates = find_duplicates_in_platform(platform_files)
                duplicates[platform.value] = platform_duplicates

                if len(platform_duplicates) > 0:
                    print(f"[DUPLICATE-CHECK] {platform.value}: Found {len(platform_duplicates)} duplicate files")
                else:
                    print(f"[DUPLICATE-CHECK] {platform.value}: No duplicate files found")

            except Exception as e:
                print(f"[DUPLICATE-CHECK] Error checking {platform.value}: {e}")
                duplicates[platform.value] = []

        total_duplicates = sum(len(files) for files in duplicates.values())
        platforms_with_duplicates = len([p for p in duplicates.values() if p])

        print(f"[DUPLICATE-CHECK] Summary: {total_duplicates} total duplicates across {platforms_with_duplicates} platforms")

        return {
            "duplicates": duplicates,
            "filter_type": "last48hours",
            "time_filter": time_filter,
            "total_platforms": len(Platform),
            "platforms_with_duplicates": platforms_with_duplicates,
            "total_duplicates": total_duplicates
        }

    except HTTPException:
        raise
    except Exception as e:
        print(f"Error in check_duplicate_files: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@app.get("/api/embed-extract/files-last48h")
async def get_files_uploaded_last48h():
    """Get all files uploaded in the last 48 hours from all platforms for embed code extraction"""
    try:
        # Get current time and calculate 48 hours ago
        now_utc = datetime.now(timezone.utc)
        forty_eight_hours_ago = now_utc - timedelta(hours=48)

        # Format for API calls
        time_filter = forty_eight_hours_ago.strftime("%Y-%m-%d %H:%M:%S")

        print(f"[EMBED-EXTRACT] Fetching files uploaded in last 48 hours from all platforms...")
        print(f"[EMBED-EXTRACT] Using UTC time filter (48h ago): {time_filter}")
        print(f"[EMBED-EXTRACT] Current UTC time: {now_utc.strftime('%Y-%m-%d %H:%M:%S')}")

        all_files = {}

        # Check each platform for files uploaded today
        for platform in Platform:
            try:
                print(f"[EMBED-EXTRACT] Checking {platform.value} for today's files...")
                if platform == Platform.LULUSTREAM:
                    platform_files = await get_lulustream_files(time_filter, None)
                elif platform == Platform.FILEMOON:
                    platform_files = await get_filemoon_files(time_filter, None)
                elif platform == Platform.STREAMP2P:
                    platform_files = await get_streamp2p_files(time_filter, None)
                elif platform == Platform.RPMSHARE:
                    platform_files = await get_rpmshare_files(time_filter, None)
                elif platform == Platform.UPNSHARE:
                    platform_files = await get_upnshare_files(time_filter, None)
                else:
                    platform_files = []

                all_files[platform.value] = platform_files
                print(f"[EMBED-EXTRACT] Found {len(platform_files)} files on {platform.value}")

            except Exception as e:
                print(f"[EMBED-EXTRACT] Error fetching files from {platform.value}: {e}")
                all_files[platform.value] = []

        # Count total files
        total_files = sum(len(files) for files in all_files.values())
        print(f"[EMBED-EXTRACT] Total files found in last 48 hours: {total_files}")

        return {
            "success": True,
            "time_range": "last_48_hours",
            "cutoff_time": time_filter,
            "current_time": now_utc.strftime("%Y-%m-%d %H:%M:%S"),
            "total_files": total_files,
            "files_by_platform": all_files
        }

    except Exception as e:
        print(f"Error in get_files_uploaded_last24h: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


def normalize_filename(filename):
    """Normalize filename for matching across platforms"""
    if not filename:
        return ""

    import re

    # Start with the original filename
    name = filename.strip()

    # Remove file extensions (comprehensive list)
    extensions = ['.mkv', '.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.m4v', '.3gp', '.ts', '.m2ts']
    for ext in extensions:
        if name.lower().endswith(ext.lower()):
            name = name[:-len(ext)]
            break

    # Replace dots with spaces (common in some platforms)
    name = name.replace('.', ' ')

    # Normalize common website variations
    name = name.replace('www.nomovie2.com', 'www nomovie2 com')
    name = name.replace('Vegamovies.is', 'Vegamovies is')
    name = name.replace('HDHub4u.Ms', 'HDHub4u Ms')

    # Remove common brackets and their contents that might vary
    name = re.sub(r'\[Downloaded From[^\]]*\]', '', name, flags=re.IGNORECASE)
    name = re.sub(r'\[.*?www[^\]]*\]', '', name, flags=re.IGNORECASE)

    # Normalize spacing and punctuation
    name = re.sub(r'[-_]+', ' ', name)  # Replace dashes and underscores with spaces
    name = re.sub(r'\s+', ' ', name)    # Replace multiple spaces with single space

    # Remove codec specifications that might vary between platforms
    name = re.sub(r'\b(x264|x265|h264|h265|hevc|avc)\b', '', name, flags=re.IGNORECASE)

    # Remove extra punctuation at the end
    name = re.sub(r'[.\-_\s]+$', '', name)

    # Clean up multiple spaces again after codec removal
    name = re.sub(r'\s+', ' ', name)

    # Convert to lowercase for better matching
    name = name.lower().strip()

    return name

@app.post("/api/embed-extract/generate-csv")
async def generate_embed_csv():
    """Generate CSV file with embed codes for all files uploaded in the last 48 hours"""
    try:
        # First get last 48 hours files
        files_response = await get_files_uploaded_last48h()
        if not files_response["success"]:
            raise HTTPException(status_code=500, detail="Failed to fetch last 48 hours files")

        files_by_platform = files_response["files_by_platform"]

        # Create a mapping of normalized filenames to embed codes
        filename_embeds = {}

        # Process each platform's files
        for platform_name, files in files_by_platform.items():
            for file_data in files:
                original_filename = file_data.get("name", "Unknown")
                normalized_filename = normalize_filename(original_filename)
                embed_code = file_data.get("embed_code", "")

                if normalized_filename and embed_code:
                    if normalized_filename not in filename_embeds:
                        filename_embeds[normalized_filename] = {
                            "original_filename": original_filename,  # Keep one original filename for display
                            "lulustream": "",
                            "streamp2p": "",
                            "rpmshare": "",
                            "filemoon": "",
                            "upnshare": ""
                        }
                    # Update original filename if current one is longer (more descriptive)
                    if len(original_filename) > len(filename_embeds[normalized_filename]["original_filename"]):
                        filename_embeds[normalized_filename]["original_filename"] = original_filename

                    filename_embeds[normalized_filename][platform_name] = embed_code

        # Generate CSV content
        csv_content = []
        csv_content.append(["Filename", "Embed Codes"])

        for normalized_filename, embeds in filename_embeds.items():
            # Use the original filename for display
            display_filename = embeds["original_filename"]

            # Create embed codes string with specific format and line gaps
            embed_lines = []
            embed_lines.append(embeds["lulustream"])  # 1st line - Lulustream
            embed_lines.append("")  # Gap
            embed_lines.append(embeds["streamp2p"])   # 2nd line - Streamp2p
            embed_lines.append("")  # Gap
            embed_lines.append(embeds["rpmshare"])    # 3rd line - Rpmshare
            embed_lines.append("")  # Gap
            embed_lines.append(embeds["filemoon"])    # 4th line - Filemoon
            embed_lines.append("")  # Gap
            embed_lines.append(embeds["upnshare"])    # 5th line - Upnshare

            # Join with newlines
            embed_codes_cell = "\n".join(embed_lines)
            csv_content.append([display_filename, embed_codes_cell])

        # Save CSV file
        current_time = files_response["current_time"]
        timestamp = current_time.replace(" ", "_").replace(":", "-")
        csv_filename = f"embed_codes_last24h_{timestamp}.csv"
        csv_filepath = os.path.join(DATA_DIR, csv_filename)

        # Ensure data directory exists
        os.makedirs(DATA_DIR, exist_ok=True)

        # Write CSV file
        with open(csv_filepath, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerows(csv_content)

        print(f"[EMBED-EXTRACT] Generated CSV file: {csv_filepath}")
        print(f"[EMBED-EXTRACT] Total files in CSV: {len(filename_embeds)}")

        return {
            "success": True,
            "csv_filename": csv_filename,
            "csv_filepath": csv_filepath,
            "total_files": len(filename_embeds),
            "time_range": "last_48_hours",
            "timestamp": current_time
        }

    except Exception as e:
        print(f"Error in generate_embed_csv: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@app.get("/api/embed-extract/download-csv")
async def download_embed_csv(filename: str):
    """Download the generated embed codes CSV file"""
    try:
        csv_filepath = os.path.join(DATA_DIR, filename)

        if not os.path.exists(csv_filepath):
            raise HTTPException(status_code=404, detail="CSV file not found")

        return FileResponse(
            path=csv_filepath,
            filename=filename,
            media_type='text/csv',
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )

    except Exception as e:
        print(f"Error in download_embed_csv: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to download CSV: {str(e)}")


@app.post("/api/duplicates/delete")
async def delete_duplicate_files(request: dict):
    """Delete selected duplicate files from a specific platform"""
    try:
        platform_name = request.get("platform")
        file_ids = request.get("file_ids", [])

        if not platform_name or not file_ids:
            raise HTTPException(status_code=400, detail="Platform and file_ids are required")

        # Find the platform enum
        platform = None
        for p in Platform:
            if p.value == platform_name:
                platform = p
                break

        if not platform:
            raise HTTPException(status_code=400, detail=f"Unknown platform: {platform_name}")

        config = PLATFORM_CONFIGS[platform]
        deleted_count = 0
        failed_count = 0

        async with aiohttp.ClientSession() as session:
            for file_id in file_ids:
                try:
                    if platform == Platform.LULUSTREAM:
                        success = await delete_lulustream_file(session, config, file_id)
                    elif platform == Platform.FILEMOON:
                        success = await delete_filemoon_file(session, config, file_id)
                    elif platform == Platform.STREAMP2P:
                        success = await delete_streamp2p_file(session, config, file_id)
                    elif platform == Platform.RPMSHARE:
                        success = await delete_rpmshare_file(session, config, file_id)
                    elif platform == Platform.UPNSHARE:
                        success = await delete_upnshare_file(session, config, file_id)
                    else:
                        success = False

                    if success:
                        deleted_count += 1
                    else:
                        failed_count += 1

                except Exception as e:
                    print(f"Error deleting file {file_id} from {platform_name}: {e}")
                    failed_count += 1

        return {
            "platform": platform_name,
            "deleted_count": deleted_count,
            "failed_count": failed_count,
            "total_requested": len(file_ids)
        }

    except HTTPException:
        raise
    except Exception as e:
        print(f"Error in delete_duplicate_files: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


# Helper functions for duplicate file checking

async def get_lulustream_files(time_filter: str = None, end_time: str = None):
    """Get files from Lulustream with simple filtering: last 48 hours or all files"""
    try:
        from datetime import timezone, timedelta as td

        config = PLATFORM_CONFIGS[Platform.LULUSTREAM]
        async with aiohttp.ClientSession() as session:
            # Get files (Lulustream API supports created parameter)
            params = {
                "key": config["api_key"],
                "per_page": 100
            }

            if time_filter:
                print(f"[LULUSTREAM] Fetching files from last 48 hours (cutoff: {time_filter})")
                # Convert UTC time_filter to minutes ago from now for Lulustream API
                cutoff_dt = datetime.strptime(time_filter, "%Y-%m-%d %H:%M:%S").replace(tzinfo=timezone.utc)
                now_utc = datetime.now(timezone.utc)
                minutes_ago = int((now_utc - cutoff_dt).total_seconds() / 60)
                params["created"] = minutes_ago
                print(f"[LULUSTREAM] Using API created parameter: {minutes_ago} minutes ago")
            else:
                print(f"[LULUSTREAM] Fetching ALL files")

            list_url = f"{config['base_url']}/file/list"
            async with session.get(list_url, params=params) as response:
                print(f"[LULUSTREAM] API Response Status: {response.status}")

                if response.status == 200:
                    result = await response.json()
                    if result.get("status") == 200 and result.get("result"):
                        files = result["result"].get("files", [])
                        print(f"[LULUSTREAM] Found {len(files)} files")

                        return [{
                            "id": file.get("file_code"),
                            "name": file.get("title"),
                            "uploaded": file.get("uploaded"),
                            "size": file.get("size"),
                            "views": file.get("views"),
                            "embed_code": f'<iframe src="https://luluvid.com/e/{file.get("file_code")}" scrolling="no" frameborder="0" width="640" height="360" allowfullscreen="true" webkitallowfullscreen="true" mozallowfullscreen="true"></iframe>',
                            "status": "ready" if file.get("canplay") == 1 else "processing"
                        } for file in files]
                else:
                    print(f"[LULUSTREAM] API Error: {response.status} - {await response.text()}")
        return []
    except Exception as e:
        print(f"[LULUSTREAM] Error getting files: {e}")
        return []


async def get_filemoon_files(time_filter: str = None, end_time: str = None):
    """Get files from FileMoon with simple filtering: last 48 hours or all files"""
    try:
        from datetime import timezone, timedelta as td

        config = PLATFORM_CONFIGS[Platform.FILEMOON]
        async with aiohttp.ClientSession() as session:
            # Get files
            params = {
                "key": config["api_key"],
                "per_page": 100
            }

            if time_filter:
                print(f"[FILEMOON] Fetching files from last 48 hours (cutoff: {time_filter})")
                # Convert UTC time_filter to minutes ago from now for FileMoon API
                cutoff_dt = datetime.strptime(time_filter, "%Y-%m-%d %H:%M:%S").replace(tzinfo=timezone.utc)
                now_utc = datetime.now(timezone.utc)
                minutes_ago = int((now_utc - cutoff_dt).total_seconds() / 60)
                params["created"] = minutes_ago
                print(f"[FILEMOON] Using API created parameter: {minutes_ago} minutes ago")
            else:
                print(f"[FILEMOON] Fetching ALL files")

            list_url = f"{config['base_url']}/file/list"
            async with session.get(list_url, params=params) as response:
                print(f"[FILEMOON] API Response Status: {response.status}")

                if response.status == 200:
                    result = await response.json()
                    if result.get("status") == 200 and result.get("result"):
                        result_data = result["result"]

                        # FileMoon API returns result as a dict with 'files' property
                        if isinstance(result_data, dict) and "files" in result_data:
                            files = result_data["files"]
                        else:
                            print(f"[FILEMOON] API returned unexpected result format: {type(result_data)} - {result_data}")
                            return []

                        # Ensure files is a list and each file is a dictionary
                        if not isinstance(files, list):
                            print(f"[FILEMOON] API files is not a list: {type(files)} - {files}")
                            return []

                        print(f"[FILEMOON] Found {len(files)} files")

                        processed_files = []
                        for file in files:
                            # Skip if file is not a dictionary (could be string, None, etc.)
                            if not isinstance(file, dict):
                                print(f"[FILEMOON] API returned non-dict file object: {type(file)} - {file}")
                                continue

                            # Ensure required fields exist
                            filecode = file.get("file_code")  # Correct field name
                            if not filecode:
                                continue

                            processed_files.append({
                                "id": filecode,
                                "name": file.get("title", ""),  # Correct field name
                                "uploaded": file.get("uploaded", ""),
                                "size": file.get("size", ""),
                                "views": file.get("views", 0),
                                "embed_code": f'<iframe src="https://filemoon.to/e/{filecode}/{file.get("title", "")}" frameborder="0" marginwidth="0" marginheight="0" scrolling="no" width="640" height="360" allowfullscreen></iframe>',
                                "status": "ready" if file.get("canplay") == 1 else "processing"
                            })

                        return processed_files
                else:
                    print(f"[FILEMOON] API Error: {response.status} - {await response.text()}")
        return []
    except Exception as e:
        print(f"[FILEMOON] Error getting files: {e}")
        return []


async def get_streamp2p_files(time_filter: str = None, end_time: str = None):
    """Get files from StreamP2P with simple filtering: last 48 hours or all files"""
    try:
        from datetime import timezone, timedelta as td

        config = PLATFORM_CONFIGS[Platform.STREAMP2P]
        async with aiohttp.ClientSession() as session:
            # Ensure proper authentication headers
            headers = {
                "api-token": config["api_key"],
                "Content-Type": "application/json"
            }
            params = {"perPage": 100}

            if time_filter:
                print(f"[STREAMP2P] Fetching videos from last 48 hours (cutoff: {time_filter})")
            else:
                print(f"[STREAMP2P] Fetching ALL videos")

            # Use correct API endpoint for video management (uploaded videos)
            video_list_url = f"{config['base_url']}/video/manage"
            async with session.get(video_list_url, headers=headers, params=params) as response:
                print(f"[STREAMP2P] /api/v1/video/manage Response Status: {response.status}")

                if response.status == 200:
                    result = await response.json()
                    print(f"[STREAMP2P] Response Keys: {list(result.keys()) if isinstance(result, dict) else 'Not a dict'}")

                    if result.get("data"):
                        files = result["data"]
                        print(f"[STREAMP2P] Found {len(files)} videos")

                        filtered_files = []
                        for file in files:
                            if not isinstance(file, dict):
                                continue

                            # Apply time filtering if specified
                            include_file = True
                            if time_filter:
                                file_date_str = file.get("createdAt", "")
                                if file_date_str:
                                    try:
                                        # Parse file date (handle various ISO formats)
                                        if 'T' in file_date_str:
                                            # ISO format with T separator
                                            file_dt_str = file_date_str.replace('T', ' ').split('.')[0]
                                        else:
                                            # Already in YYYY-MM-DD HH:MM:SS format
                                            file_dt_str = file_date_str.split('.')[0]

                                        file_dt = datetime.strptime(file_dt_str, "%Y-%m-%d %H:%M:%S").replace(tzinfo=timezone.utc)
                                        cutoff_dt = datetime.strptime(time_filter, "%Y-%m-%d %H:%M:%S").replace(tzinfo=timezone.utc)

                                        # Hours cutoff check (cumulative - from cutoff time to now)
                                        if file_dt < cutoff_dt:
                                            include_file = False
                                    except ValueError as e:
                                        print(f"[STREAMP2P] Error parsing file date {file_date_str}: {e}")
                                        include_file = False

                            if include_file:
                                filtered_files.append({
                                    "id": file.get("id"),
                                    "name": file.get("name", ""),
                                    "uploaded": file.get("createdAt", ""),
                                    "size": file.get("size", 0),
                                    "views": file.get("play", 0),  # StreamP2P uses "play" for views
                                    "embed_code": f'<iframe src="https://streamdb.p2pstream.online/#{file.get("id")}" width="100%" height="100%" frameborder="0" allowfullscreen></iframe>',
                                    "status": file.get("status", "unknown")
                                })

                        print(f"[STREAMP2P] Returning {len(filtered_files)} videos")
                        return filtered_files
                else:
                    print(f"[STREAMP2P] API Error: {response.status} - {await response.text()}")
        return []
    except Exception as e:
        print(f"[STREAMP2P] Error getting files: {e}")
        return []


async def get_rpmshare_files(time_filter: str = None, end_time: str = None):
    """Get files from RPMShare with simple filtering: last 48 hours or all files"""
    try:
        from datetime import timezone, timedelta as td

        config = PLATFORM_CONFIGS[Platform.RPMSHARE]
        async with aiohttp.ClientSession() as session:
            # Ensure proper authentication headers
            headers = {
                "api-token": config["api_key"],
                "Content-Type": "application/json"
            }
            params = {"perPage": 100}

            if time_filter:
                print(f"[RPMSHARE] Fetching videos from last 48 hours (cutoff: {time_filter})")
            else:
                print(f"[RPMSHARE] Fetching ALL videos")

            # Use correct API endpoint for video management (uploaded videos)
            video_list_url = f"{config['base_url']}/video/manage"
            async with session.get(video_list_url, headers=headers, params=params) as response:
                print(f"[RPMSHARE] /api/v1/video/manage Response Status: {response.status}")

                if response.status == 200:
                    result = await response.json()
                    print(f"[RPMSHARE] Response Keys: {list(result.keys()) if isinstance(result, dict) else 'Not a dict'}")

                    if result.get("data"):
                        files = result["data"]
                        print(f"[RPMSHARE] Found {len(files)} videos")

                        filtered_files = []
                        for file in files:
                            if not isinstance(file, dict):
                                continue

                            # Apply time filtering if specified
                            include_file = True
                            if time_filter:
                                file_date_str = file.get("createdAt", "")
                                if file_date_str:
                                    try:
                                        # Parse file date (handle various ISO formats)
                                        if 'T' in file_date_str:
                                            # ISO format with T separator
                                            file_dt_str = file_date_str.replace('T', ' ').split('.')[0]
                                        else:
                                            # Already in YYYY-MM-DD HH:MM:SS format
                                            file_dt_str = file_date_str.split('.')[0]

                                        file_dt = datetime.strptime(file_dt_str, "%Y-%m-%d %H:%M:%S").replace(tzinfo=timezone.utc)
                                        cutoff_dt = datetime.strptime(time_filter, "%Y-%m-%d %H:%M:%S").replace(tzinfo=timezone.utc)

                                        # Hours cutoff check (cumulative - from cutoff time to now)
                                        if file_dt < cutoff_dt:
                                            include_file = False
                                    except ValueError as e:
                                        print(f"[RPMSHARE] Error parsing file date {file_date_str}: {e}")
                                        include_file = False

                            if include_file:
                                filtered_files.append({
                                    "id": file.get("id"),
                                    "name": file.get("name", ""),
                                    "uploaded": file.get("createdAt", ""),
                                    "size": file.get("size", 0),
                                    "views": file.get("play", 0),  # RPMShare uses "play" for views
                                    "embed_code": f'<iframe src="https://streamdb.rpmstream.online/#{file.get("id")}" width="100%" height="100%" frameborder="0" allowfullscreen></iframe>',
                                    "status": file.get("status", "unknown")
                                })

                        print(f"[RPMSHARE] Returning {len(filtered_files)} videos")
                        return filtered_files
                else:
                    print(f"[RPMSHARE] API Error: {response.status} - {await response.text()}")
        return []
    except Exception as e:
        print(f"[RPMSHARE] Error getting files: {e}")
        return []


async def get_upnshare_files(time_filter: str = None, end_time: str = None):
    """Get files from UPnShare with simple filtering: last 48 hours or all files"""
    try:
        from datetime import timezone, timedelta as td

        config = PLATFORM_CONFIGS[Platform.UPNSHARE]
        async with aiohttp.ClientSession() as session:
            # Ensure proper authentication headers
            headers = {
                "api-token": config["api_key"],
                "Content-Type": "application/json"
            }
            params = {"perPage": 100}

            if time_filter:
                print(f"[UPNSHARE] Fetching videos from last 48 hours (cutoff: {time_filter})")
            else:
                print(f"[UPNSHARE] Fetching ALL videos")

            # Use correct API endpoint for video management (uploaded videos)
            video_list_url = f"{config['base_url']}/video/manage"
            async with session.get(video_list_url, headers=headers, params=params) as response:
                print(f"[UPNSHARE] /api/v1/video/manage Response Status: {response.status}")

                if response.status == 200:
                    result = await response.json()
                    print(f"[UPNSHARE] Response Keys: {list(result.keys()) if isinstance(result, dict) else 'Not a dict'}")

                    if result.get("data"):
                        files = result["data"]
                        print(f"[UPNSHARE] Found {len(files)} videos")

                        filtered_files = []
                        for file in files:
                            if not isinstance(file, dict):
                                continue

                            # Apply time filtering if specified
                            include_file = True
                            if time_filter:
                                file_date_str = file.get("createdAt", "")
                                if file_date_str:
                                    try:
                                        # Parse file date (handle various ISO formats)
                                        if 'T' in file_date_str:
                                            # ISO format with T separator
                                            file_dt_str = file_date_str.replace('T', ' ').split('.')[0]
                                        else:
                                            # Already in YYYY-MM-DD HH:MM:SS format
                                            file_dt_str = file_date_str.split('.')[0]

                                        file_dt = datetime.strptime(file_dt_str, "%Y-%m-%d %H:%M:%S").replace(tzinfo=timezone.utc)
                                        cutoff_dt = datetime.strptime(time_filter, "%Y-%m-%d %H:%M:%S").replace(tzinfo=timezone.utc)

                                        # Hours cutoff check (cumulative - from cutoff time to now)
                                        if file_dt < cutoff_dt:
                                            include_file = False
                                    except ValueError as e:
                                        print(f"[UPNSHARE] Error parsing file date {file_date_str}: {e}")
                                        include_file = False

                            if include_file:
                                filtered_files.append({
                                    "id": file.get("id"),
                                    "name": file.get("name", ""),
                                    "uploaded": file.get("createdAt", ""),
                                    "size": file.get("size", 0),
                                    "views": file.get("play", 0),  # UPnShare uses "play" for views
                                    "embed_code": f'<iframe src="https://streamdb.upns.online/#{file.get("id")}" width="100%" height="100%" frameborder="0" allowfullscreen></iframe>',
                                    "status": file.get("status", "unknown")
                                })

                        print(f"[UPNSHARE] Returning {len(filtered_files)} videos")
                        return filtered_files
                else:
                    print(f"[UPNSHARE] API Error: {response.status} - {await response.text()}")
        return []
    except Exception as e:
        print(f"[UPNSHARE] Error getting files: {e}")
        return []


def find_duplicates_in_platform(files):
    """Find duplicate files within a platform based on filename similarity"""
    if len(files) < 2:
        return []

    duplicates = []
    seen_names = {}

    for file in files:
        name = file.get("name", "").lower().strip()
        if not name:
            continue

        # Normalize filename for comparison
        normalized_name = name.replace(" ", "").replace("-", "").replace("_", "")

        # Check for exact matches or very similar names
        for seen_name, seen_files in seen_names.items():
            similarity = calculate_similarity(normalized_name, seen_name)
            if similarity > 0.8:  # 80% similarity threshold
                # Add both files as duplicates
                if seen_files not in duplicates:
                    duplicates.extend(seen_files)
                duplicates.append(file)
                seen_files.append(file)
                break
        else:
            seen_names[normalized_name] = [file]

    return duplicates


def calculate_similarity(str1, str2):
    """Calculate similarity between two strings using simple ratio"""
    if not str1 or not str2:
        return 0

    # Simple similarity calculation
    longer = str1 if len(str1) > len(str2) else str2
    shorter = str2 if len(str1) > len(str2) else str1

    if len(longer) == 0:
        return 1.0

    # Count matching characters
    matches = sum(1 for a, b in zip(shorter, longer) if a == b)
    return matches / len(longer)


# Delete functions for each platform

async def delete_lulustream_file(session: aiohttp.ClientSession, config: dict, file_id: str):
    """Delete a file from Lulustream - Note: Lulustream doesn't provide a direct file delete API"""
    try:
        # Lulustream doesn't provide a direct file delete API in their public documentation
        # The /api/file/url_actions endpoint with delete_code only removes files from upload queue, not uploaded files
        # Users need to delete files manually through the Lulustream web interface
        print(f"Lulustream delete not available via API for file {file_id} - manual deletion required")
        return False
    except Exception as e:
        print(f"Error with Lulustream file deletion {file_id}: {e}")
        return False


async def delete_filemoon_file(session: aiohttp.ClientSession, config: dict, file_id: str):
    """Delete a file from FileMoon - Note: FileMoon doesn't have direct delete API in public docs"""
    try:
        # FileMoon doesn't provide a direct file delete API in their public documentation
        # Only "Remove Remote Upload" is available which removes files from upload queue, not uploaded files
        # Users may need to delete files manually through the FileMoon web interface
        print(f"FileMoon delete not available via API for file {file_id} - manual deletion required")
        return False
    except Exception as e:
        print(f"Error with FileMoon file deletion {file_id}: {e}")
        return False


async def delete_streamp2p_file(session: aiohttp.ClientSession, config: dict, file_id: str):
    """Delete a file from StreamP2P"""
    try:
        delete_url = f"{config['base_url']}/video/manage/{file_id}"
        headers = config.get("headers", {})

        async with session.delete(delete_url, headers=headers) as response:
            print(f"StreamP2P delete response for {file_id}: {response.status}")
            return response.status == 204
    except Exception as e:
        print(f"Error deleting StreamP2P file {file_id}: {e}")
        return False


async def delete_rpmshare_file(session: aiohttp.ClientSession, config: dict, file_id: str):
    """Delete a file from RPMShare"""
    try:
        delete_url = f"{config['base_url']}/video/manage/{file_id}"
        headers = config.get("headers", {})

        async with session.delete(delete_url, headers=headers) as response:
            print(f"RPMShare delete response for {file_id}: {response.status}")
            return response.status == 204
    except Exception as e:
        print(f"Error deleting RPMShare file {file_id}: {e}")
        return False


async def delete_upnshare_file(session: aiohttp.ClientSession, config: dict, file_id: str):
    """Delete a file from UpnShare"""
    try:
        delete_url = f"{config['base_url']}/video/manage/{file_id}"
        headers = config.get("headers", {})

        async with session.delete(delete_url, headers=headers) as response:
            print(f"UpnShare delete response for {file_id}: {response.status}")
            return response.status == 204
    except Exception as e:
        print(f"Error deleting UpnShare file {file_id}: {e}")
        return False


# Static files for audio
audio_dir = os.path.join(os.path.dirname(__file__), 'audio')
app.mount("/audio", StaticFiles(directory=audio_dir), name="audio")

# Lulustream-specific post-upload task functions
async def find_lulustream_duplicates(session: aiohttp.ClientSession, task: UploadTask, config: dict, primary_file_code: str) -> list:
    """Find duplicate files on Lulustream by searching for similar filenames"""
    try:
        # Search for files with similar names
        search_terms = [
            task.filename.rsplit('.', 1)[0],  # Filename without extension
            task.filename[:20],  # First 20 characters
            primary_file_code
        ]

        all_duplicates = []
        for search_term in search_terms:
            # Use file list API to search for duplicates
            list_url = f"{config['base_url']}/file/list"
            params = {
                "key": config["api_key"],
                "title": search_term,
                "per_page": 50
            }

            async with session.get(list_url, params=params) as response:
                if response.status == 200:
                    result = await response.json()
                    if result.get("status") == 200 and result.get("result"):
                        files = result["result"]
                        for file_info in files:
                            if file_info.get("filecode") and file_info["filecode"] not in [f["file_code"] for f in all_duplicates]:
                                all_duplicates.append({
                                    "file_code": file_info["filecode"],
                                    "name": file_info.get("name", ""),
                                    "canplay": file_info.get("canplay", 0),
                                    "status": file_info.get("status", "")
                                })

        return all_duplicates
    except Exception as e:
        print(f"[LULUSTREAM-POST] Error finding duplicates: {e}")
        return [{"file_code": primary_file_code, "name": task.filename, "canplay": 0, "status": ""}]

async def find_best_working_lulustream_file(session: aiohttp.ClientSession, config: dict, duplicate_files: list) -> dict:
    """Test each duplicate file to find the best working one"""
    try:
        working_files = []

        for file_info in duplicate_files:
            file_code = file_info["file_code"]

            # Get detailed file info
            info_url = f"{config['base_url']}{config['info_endpoint']}"
            info_params = {"key": config["api_key"], "file_code": file_code}

            async with session.get(info_url, params=info_params) as response:
                if response.status == 200:
                    result = await response.json()
                    if result.get("status") == 200 and result.get("result"):
                        file_data = result["result"][0] if isinstance(result["result"], list) else result["result"]

                        can_play = file_data.get("canplay")
                        status = file_data.get("status")
                        title = file_data.get("title", "")

                        # Check for DMCA or error indicators
                        error_indicators = [
                            "dmca violation", "copyright violation", "content removed",
                            "violation reported", "no video is available", "video has been deleted"
                        ]

                        has_error = any(indicator in title.lower() for indicator in error_indicators)

                        if not has_error and (can_play in [1, "1", True] or status in ["ready", "completed", "active"]):
                            working_files.append({
                                "file_code": file_code,
                                "name": file_data.get("name", ""),
                                "canplay": can_play,
                                "status": status,
                                "title": title,
                                "score": (1 if can_play in [1, "1", True] else 0) + (1 if status in ["ready", "completed", "active"] else 0)
                            })

        # Return the file with the highest score (most likely to work)
        if working_files:
            return max(working_files, key=lambda x: x["score"])

        return None
    except Exception as e:
        print(f"[LULUSTREAM-POST] Error testing files: {e}")
        return None

async def update_csv_with_best_file(task: UploadTask, platform: Platform, best_file: dict):
    """Update the CSV file with the best working file's embed code"""
    try:
        embed_code = f'<iframe src="https://luluvid.com/e/{best_file["file_code"]}" frameborder="0" marginwidth="0" marginheight="0" scrolling="no" width="640" height="360" allowfullscreen></iframe>'

        # Update the embed code in the CSV
        await _save_embed_code(task, platform, embed_code, best_file["file_code"], best_file["name"])
        print(f"[LULUSTREAM-POST] Updated CSV with best file: {best_file['file_code']}")

    except Exception as e:
        print(f"[LULUSTREAM-POST] Error updating CSV: {e}")

async def delete_lulustream_duplicates(session: aiohttp.ClientSession, config: dict, duplicate_files: list, keep_file_code: str):
    """Delete duplicate files except the one we want to keep"""
    try:
        deleted_count = 0
        for file_info in duplicate_files:
            file_code = file_info["file_code"]

            if file_code != keep_file_code:
                # Delete the duplicate file
                delete_url = f"{config['base_url']}/file/delete"
                params = {
                    "key": config["api_key"],
                    "file_code": file_code
                }

                async with session.get(delete_url, params=params) as response:
                    if response.status == 200:
                        result = await response.json()
                        if result.get("status") == 200:
                            print(f"[LULUSTREAM-POST] Deleted duplicate file: {file_code}")
                            deleted_count += 1
                        else:
                            print(f"[LULUSTREAM-POST] Failed to delete {file_code}: {result.get('msg', 'Unknown error')}")

                # Small delay between deletions
                await asyncio.sleep(1)

        print(f"[LULUSTREAM-POST] Deleted {deleted_count} duplicate files, kept: {keep_file_code}")

    except Exception as e:
        print(f"[LULUSTREAM-POST] Error deleting duplicates: {e}")

async def check_lulustream_dmca_false_positives(session: aiohttp.ClientSession, config: dict, file_codes: list):
    """Check for DMCA false positives and report them"""
    try:
        for file_code in file_codes:
            info_url = f"{config['base_url']}{config['info_endpoint']}"
            info_params = {"key": config["api_key"], "file_code": file_code}

            async with session.get(info_url, params=info_params) as response:
                if response.status == 200:
                    result = await response.json()
                    if result.get("status") == 200 and result.get("result"):
                        file_data = result["result"][0] if isinstance(result["result"], list) else result["result"]
                        title = file_data.get("title", "")

                        # Check for DMCA false positive indicators
                        dmca_indicators = [
                            "no video is available",
                            "video has been deleted due to a dmca complaint",
                            "dmca violation reported",
                            "copyright violation"
                        ]

                        if any(indicator in title.lower() for indicator in dmca_indicators):
                            print(f"[LULUSTREAM-DMCA] FALSE POSITIVE DETECTED: {file_code} - {title}")

                            # Broadcast DMCA notification for manual review
                            await broadcast_dmca_notification_detailed(file_code, title)

    except Exception as e:
        print(f"[LULUSTREAM-POST] Error checking DMCA: {e}")

async def broadcast_dmca_notification_detailed(file_code: str, title: str):
    """Broadcast detailed DMCA notification for manual review"""
    try:
        notification = {
            "type": "dmca_false_positive",
            "file_code": file_code,
            "title": title,
            "message": f"DMCA False Positive Detected: {file_code}",
            "action_required": "Manual review and replacement needed",
            "timestamp": datetime.now().isoformat()
        }

        # Broadcast to all connected clients
        await manager.broadcast(notification)
        print(f"[LULUSTREAM-DMCA] Broadcasted DMCA notification for {file_code}")

    except Exception as e:
        print(f"[LULUSTREAM-POST] Error broadcasting DMCA notification: {e}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "server:app",
        host=os.environ.get("BACKEND_HOST", "0.0.0.0"),
        port=int(os.environ.get("BACKEND_PORT", "8001")),
        reload=True
    )