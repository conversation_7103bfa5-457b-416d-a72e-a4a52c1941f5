import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  Upload,
  Trash2,
  <PERSON><PERSON><PERSON>cle,
  XCircle,
  Clock,
  AlertTriangle,
  Loader2,
  Plus,
  Monitor,
  Zap,
  BarChart3,
  Search,
  FileX,
  Code,
  Download,
  Eye
} from 'lucide-react';
import toast, { Toaster } from 'react-hot-toast';
import './App.css';

// Get backend URL from environment
const BACKEND_URL = process.env.REACT_APP_BACKEND_URL || 'http://localhost:8001';

// Platform configurations
const PLATFORMS = [
  { name: 'lulustream', display: 'Lulustream', color: 'purple', icon: '🟣' },
  { name: 'streamp2p', display: 'StreamP2P', color: 'blue', icon: '🔵' },
  { name: 'rpmshare', display: 'RPMShare', color: 'green', icon: '🟢' },
  { name: 'filemoon', display: 'FileMoon', color: 'yellow', icon: '🟡' },
  { name: 'upnshare', display: 'UpnShare', color: 'pink', icon: '🔴' }
];

// Fixed color hex map for consistent circular icons
const PLATFORM_COLOR_HEX = {
  purple: '#8b5cf6',
  blue: '#3b82f6',
  green: '#22c55e',
  yellow: '#f59e0b',
  pink: '#ec4899',
};

// Duplicate File Checker Component
function DuplicateFileChecker({ duplicateCheckerState, setDuplicateCheckerState, platforms, platformColorHex }) {
  const [isSearching, setIsSearching] = useState(false);

  const handleSearch = async () => {
    setIsSearching(true);
    try {
      const response = await fetch(`${BACKEND_URL}/api/duplicates/check`);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      setDuplicateCheckerState(prev => ({
        ...prev,
        duplicateFiles: data.duplicates || {},
        selectedFiles: {}
      }));

      const totalDuplicates = Object.values(data.duplicates || {}).reduce((sum, files) => sum + files.length, 0);

      if (totalDuplicates > 0) {
        toast.success(`Found ${totalDuplicates} duplicate files across ${data.platforms_with_duplicates} platforms`);
      } else {
        toast.success('No duplicate files found in the last 24 hours');
      }
    } catch (error) {
      console.error('Error checking duplicates:', error);
      toast.error(`Failed to check for duplicates: ${error.message}`);
    } finally {
      setIsSearching(false);
    }
  };

  const handleFileSelection = (platform, fileId, checked) => {
    setDuplicateCheckerState(prev => ({
      ...prev,
      selectedFiles: {
        ...prev.selectedFiles,
        [platform]: {
          ...prev.selectedFiles[platform],
          [fileId]: checked
        }
      }
    }));
  };

  const handleDeleteSelected = async (platform) => {
    const selectedFiles = duplicateCheckerState.selectedFiles[platform] || {};
    const filesToDelete = Object.keys(selectedFiles).filter(fileId => selectedFiles[fileId]);

    if (filesToDelete.length === 0) {
      toast.error('No files selected for deletion');
      return;
    }

    // Confirm deletion
    if (!window.confirm(`Are you sure you want to delete ${filesToDelete.length} files from ${platform}? This action cannot be undone.`)) {
      return;
    }

    try {
      const response = await fetch(`${BACKEND_URL}/api/duplicates/delete`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          platform: platform,
          file_ids: filesToDelete
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();

      if (result.deleted_count > 0) {
        toast.success(`Successfully deleted ${result.deleted_count} files from ${platform}`);
      }

      if (result.failed_count > 0) {
        toast.error(`Failed to delete ${result.failed_count} files from ${platform}`);
      }

      // Refresh the duplicate list
      handleSearch();
    } catch (error) {
      console.error('Error deleting files:', error);
      toast.error(`Failed to delete files: ${error.message}`);
    }
  };

  const handleClearList = (platform) => {
    setDuplicateCheckerState(prev => ({
      ...prev,
      duplicateFiles: {
        ...prev.duplicateFiles,
        [platform]: []
      },
      selectedFiles: {
        ...prev.selectedFiles,
        [platform]: {}
      }
    }));
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center space-x-3">
        <FileX className="w-6 h-6 text-purple-400" />
        <h2 className="text-xl font-semibold">Duplicate File Checker</h2>
      </div>

      {/* Search Controls */}
      <div className="border border-gray-600 rounded-xl p-6" style={{backgroundColor: '#242424'}}>
        <div className="flex items-end gap-4 mb-6">
          {/* Filter Description */}
          <div className="flex-1">
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Duplicate File Search - Last 24 Hours
            </label>
            <div className="px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white">
              Last 24 Hours (Cumulative)
            </div>
            <p className="text-xs text-gray-500 mt-1">
              Show cumulative duplicate files from last 24 hours till now (IST timezone)
            </p>
          </div>

          {/* Search Button */}
          <div className="flex-shrink-0">
            <button
              onClick={handleSearch}
              disabled={isSearching}
              className="px-6 py-2 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg font-medium transition-colors duration-200 flex items-center justify-center space-x-2"
              style={{height: '42px'}}
            >
              {isSearching ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span>Searching...</span>
                </>
              ) : (
                <>
                  <Search className="w-4 h-4" />
                  <span>Check Duplicates</span>
                </>
              )}
            </button>
          </div>
        </div>

        <div className="bg-gray-800/50 rounded-lg p-4">
          <h4 className="text-sm font-medium text-gray-300 mb-2">How it works:</h4>
          <ul className="text-xs text-gray-400 space-y-1">
            <li>• <strong>Last 24 Hours:</strong> Shows duplicate files uploaded in the last 24 hours from current IST time</li>
            <li>• Files are considered duplicates if they have the same name and size within the same platform</li>
            <li>• If no duplicates are found for a platform, an appropriate message will be shown</li>
            <li>• You can select and delete duplicate files using the checkboxes and delete buttons</li>
          </ul>
        </div>
      </div>

      {/* Platform Sections */}
      <div className="space-y-6">
        {platforms.map((platform) => (
          <PlatformDuplicateSection
            key={platform.name}
            platform={platform}
            platformColorHex={platformColorHex}
            duplicateFiles={duplicateCheckerState.duplicateFiles[platform.name] || []}
            selectedFiles={duplicateCheckerState.selectedFiles[platform.name] || {}}
            onFileSelection={(fileId, checked) => handleFileSelection(platform.name, fileId, checked)}
            onDeleteSelected={() => handleDeleteSelected(platform.name)}
            onClearList={() => handleClearList(platform.name)}
          />
        ))}
      </div>
    </div>
  );
}

// Platform Duplicate Section Component
function PlatformDuplicateSection({
  platform,
  platformColorHex,
  duplicateFiles,
  selectedFiles,
  onFileSelection,
  onDeleteSelected,
  onClearList
}) {
  const hasFiles = duplicateFiles.length > 0;
  const selectedCount = Object.values(selectedFiles).filter(Boolean).length;

  return (
    <div className="border border-gray-600 rounded-xl p-6" style={{backgroundColor: '#242424'}}>
      {/* Platform Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <span
            className="inline-flex items-center justify-center w-6 h-6 rounded-full"
            style={{ backgroundColor: platformColorHex[platform.color] }}
          />
          <h3 className="text-lg font-semibold text-white">{platform.display}</h3>
          {hasFiles && (
            <span className="px-2 py-1 bg-red-900/30 text-red-400 text-sm rounded-full">
              {duplicateFiles.length} duplicate{duplicateFiles.length !== 1 ? 's' : ''}
            </span>
          )}
        </div>

        {hasFiles && (
          <div className="flex items-center space-x-2">
            <button
              onClick={onClearList}
              className="px-3 py-1 bg-gray-600 hover:bg-gray-700 text-white text-sm rounded-lg transition-colors duration-200"
            >
              Clear List
            </button>
            {platform.name === 'filemoon' ? (
              <div className="px-3 py-1 bg-yellow-900/30 text-yellow-400 text-sm rounded-lg">
                Manual deletion required
              </div>
            ) : (
              <button
                onClick={onDeleteSelected}
                disabled={selectedCount === 0}
                className="px-3 py-1 bg-red-600 hover:bg-red-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white text-sm rounded-lg transition-colors duration-200"
              >
                Delete Selected ({selectedCount})
              </button>
            )}
          </div>
        )}
      </div>

      {/* Files List */}
      {hasFiles ? (
        <div className="space-y-3">
          {duplicateFiles.map((file, index) => (
            <div key={file.id || index} className="border border-gray-700 rounded-lg p-4 bg-gray-800/50">
              <div className="flex items-start space-x-3">
                <input
                  type="checkbox"
                  checked={selectedFiles[file.id] || false}
                  onChange={(e) => onFileSelection(file.id, e.target.checked)}
                  className="mt-1 w-4 h-4 text-purple-600 bg-gray-700 border-gray-600 rounded focus:ring-purple-500"
                />
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="text-white font-medium truncate">{file.name || file.title}</h4>
                    <span className="text-xs text-gray-400">{file.uploaded || file.created_at}</span>
                  </div>

                  {file.embed_code && (
                    <div className="mb-2">
                      <p className="text-xs text-gray-400 mb-1">Embed Code:</p>
                      <code className="text-xs bg-gray-900 text-green-400 p-2 rounded block overflow-x-auto">
                        {file.embed_code}
                      </code>
                    </div>
                  )}

                  <div className="flex items-center space-x-4 text-gray-400" style={{fontSize: '0.8125rem'}}>
                    {file.size && <span>Size: {file.size}</span>}
                    {file.views !== undefined && <span>Views: {file.views}</span>}
                    {file.status && <span>Status: {file.status}</span>}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-8">
          <FileX className="w-12 h-12 text-gray-600 mx-auto mb-3" />
          <p className="text-gray-400">No duplicate files found for this platform</p>
        </div>
      )}
    </div>
  );
}

// Embed Code Extract Component
function EmbedCodeExtract({ embedExtractState, setEmbedExtractState, platforms, platformColorHex }) {
  const [filesData, setFilesData] = useState(null);
  const [isLoadingFiles, setIsLoadingFiles] = useState(false);
  const [showFiles, setShowFiles] = useState(false);

  // Audio notification functions
  const playSuccessSound = () => {
    const audio = new Audio('/audio/completion.MP3');
    audio.play().catch(e => console.log('Audio play failed:', e));
  };

  const playErrorSound = () => {
    const audio = new Audio('/audio/error.mp3');
    audio.play().catch(e => console.log('Audio play failed:', e));
  };

  // Fetch files from last 48 hours
  const handleFetchFiles = async () => {
    setIsLoadingFiles(true);
    try {
      const response = await fetch(`${BACKEND_URL}/api/embed-extract/files-last48h`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const result = await response.json();
      setFilesData(result);
      setShowFiles(true);
      toast.success(`Found ${result.total_files} files from last 48 hours`);
    } catch (error) {
      console.error('Error fetching files:', error);
      toast.error(`Failed to fetch files: ${error.message}`);
    } finally {
      setIsLoadingFiles(false);
    }
  };

  // Clear the displayed files queue
  const handleClearQueue = () => {
    setFilesData(null);
    setShowFiles(false);
    toast.success('Files queue cleared');
  };

  const handleGenerateCSV = async () => {
    setEmbedExtractState(prev => ({
      ...prev,
      isGenerating: true,
      error: null,
      csvReady: false
    }));

    try {
      const response = await fetch(`${BACKEND_URL}/api/embed-extract/generate-csv`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      if (result.success) {
        setEmbedExtractState(prev => ({
          ...prev,
          isGenerating: false,
          csvReady: true,
          csvFilename: result.csv_filename,
          totalFiles: result.total_files,
          date: result.date,
          error: null
        }));

        playSuccessSound();
        toast.success(`CSV generated successfully! ${result.total_files} files processed.`);
      } else {
        throw new Error('Failed to generate CSV');
      }
    } catch (error) {
      console.error('Error generating CSV:', error);
      setEmbedExtractState(prev => ({
        ...prev,
        isGenerating: false,
        error: error.message,
        csvReady: false
      }));

      playErrorSound();
      toast.error(`Failed to generate CSV: ${error.message}`);
    }
  };

  const handleDownloadCSV = async () => {
    if (!embedExtractState.csvFilename) {
      toast.error('No CSV file available for download');
      return;
    }

    try {
      const response = await fetch(`${BACKEND_URL}/api/embed-extract/download-csv?filename=${encodeURIComponent(embedExtractState.csvFilename)}`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = embedExtractState.csvFilename;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      toast.success('CSV file downloaded successfully!');
    } catch (error) {
      console.error('Error downloading CSV:', error);
      toast.error(`Failed to download CSV: ${error.message}`);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-3 mb-6">
        <Code className="w-6 h-6 text-green-400" />
        <h2 className="text-xl font-semibold">Embed Code Extract</h2>
      </div>

      {/* Description */}
      <div className="bg-gray-800/50 rounded-lg p-4">
        <h4 className="text-sm font-medium text-gray-300 mb-2">How it works:</h4>
        <ul className="text-xs text-gray-400 space-y-1">
          <li>• <strong>Preview Files:</strong> Click "Show Files" to preview all files uploaded in the LAST 48 HOURS from all 5 platforms</li>
          <li>• <strong>Generate CSV:</strong> Fetches all files and generates embed codes for each platform</li>
          <li>• <strong>Embed Format:</strong> Each file gets embed codes from Lulustream, StreamP2P, RPMShare, FileMoon, and UpnShare</li>
          <li>• <strong>CSV Format:</strong> Column 1 = Filename, Column 2 = All 5 embed codes with line gaps between them</li>
          <li>• <strong>Download:</strong> Click "Download CSV" to save the file to your preferred location</li>
        </ul>
      </div>

      {/* File Preview Section */}
      <div className="bg-gray-800/50 rounded-lg p-4">
        <div className="flex items-center justify-between mb-4">
          <h4 className="text-sm font-medium text-gray-300">Files from Last 48 Hours</h4>
          <div className="flex items-center space-x-2">
            <button
              onClick={handleFetchFiles}
              disabled={isLoadingFiles}
              className="flex items-center space-x-2 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200"
            >
              {isLoadingFiles ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span>Loading...</span>
                </>
              ) : (
                <>
                  <Eye className="w-4 h-4" />
                  <span>Show Files</span>
                </>
              )}
            </button>

            {showFiles && (
              <button
                onClick={handleClearQueue}
                className="flex items-center space-x-2 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200"
              >
                <Trash2 className="w-4 h-4" />
                <span>Clear Queue</span>
              </button>
            )}
          </div>
        </div>

        {showFiles && filesData && (
          <div className="space-y-4">
            <div className="text-sm text-gray-300">
              <p><strong>Time Range:</strong> Last 48 Hours ({filesData.cutoff_time} to {filesData.current_time} UTC)</p>
              <p><strong>Total Files Found:</strong> {filesData.total_files}</p>
            </div>

            {filesData.total_files > 0 ? (
              <div className="space-y-3">
                {Object.entries(filesData.files_by_platform).map(([platform, files]) => (
                  <div key={platform} className="bg-gray-700/50 rounded-lg p-3">
                    <div className="flex items-center space-x-2 mb-2">
                      <div
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: platformColorHex[platform] }}
                      ></div>
                      <span className="text-sm font-medium text-gray-200 capitalize">{platform}</span>
                      <span className="text-xs text-gray-400">({files.length} files)</span>
                    </div>
                    {files.length > 0 ? (
                      <div className="space-y-1 max-h-32 overflow-y-auto">
                        {files.map((file, index) => (
                          <div key={index} className="text-xs text-gray-400 pl-5">
                            • {file.name || file.id || 'Unnamed file'}
                            {file.uploaded && <span className="ml-2 text-gray-500">({file.uploaded})</span>}
                          </div>
                        ))}
                      </div>
                    ) : (
                      <p className="text-xs text-gray-500 pl-5">No files found</p>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-4">
                <p className="text-gray-400">No files found in the last 48 hours</p>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row gap-4">
        <button
          onClick={handleGenerateCSV}
          disabled={embedExtractState.isGenerating}
          className="flex items-center justify-center space-x-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200"
        >
          {embedExtractState.isGenerating ? (
            <>
              <Loader2 className="w-4 h-4 animate-spin" />
              <span>Generating CSV...</span>
            </>
          ) : (
            <>
              <Code className="w-4 h-4" />
              <span>Generate CSV</span>
            </>
          )}
        </button>

        <button
          onClick={handleDownloadCSV}
          disabled={!embedExtractState.csvReady}
          className="flex items-center justify-center space-x-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200"
        >
          <Download className="w-4 h-4" />
          <span>Download CSV</span>
        </button>
      </div>

      {/* Status Display */}
      {embedExtractState.csvReady && (
        <div className="bg-green-900/20 border border-green-500/30 rounded-lg p-4">
          <div className="flex items-center space-x-2 mb-2">
            <CheckCircle className="w-5 h-5 text-green-400" />
            <span className="text-green-400 font-medium">CSV Ready for Download</span>
          </div>
          <div className="text-sm text-gray-300 space-y-1">
            <p><strong>Time Range:</strong> Last 48 Hours</p>
            <p><strong>Total Files:</strong> {embedExtractState.totalFiles}</p>
            <p><strong>Filename:</strong> {embedExtractState.csvFilename}</p>
          </div>
        </div>
      )}

      {/* Error Display */}
      {embedExtractState.error && (
        <div className="bg-red-900/20 border border-red-500/30 rounded-lg p-4">
          <div className="flex items-center space-x-2 mb-2">
            <XCircle className="w-5 h-5 text-red-400" />
            <span className="text-red-400 font-medium">Error</span>
          </div>
          <p className="text-sm text-gray-300">{embedExtractState.error}</p>
        </div>
      )}
    </div>
  );
}


function App() {
  // Tab management
  const [activeTab, setActiveTab] = useState('upload');

  // State management
  const [urls, setUrls] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [queueStatus, setQueueStatus] = useState({
    total_queued: 0,
    total_in_progress: 0,
    total_completed: 0,
    total_failed: 0,
    current_queue: [],
    active_uploads: [],
    completed_uploads: []
  });
  const [sessionId, setSessionId] = useState(null);
  const [platformStatus, setPlatformStatus] = useState({});
  const [retryQueueStatus, setRetryQueueStatus] = useState({});

  // Duplicate File Checker state
  const [duplicateCheckerState, setDuplicateCheckerState] = useState({
    isLoading: false,
    duplicateFiles: {},
    selectedFiles: {}
  });

  // Embed Code Extract state
  const [embedExtractState, setEmbedExtractState] = useState({
    isLoading: false,
    isGenerating: false,
    csvReady: false,
    csvFilename: '',
    totalFiles: 0,
    date: '',
    error: null
  });

  // WebSocket connection
  const wsRef = useRef(null);
  const reconnectTimeoutRef = useRef(null);

  // Enhanced Audio notification system for background operation
  const audioContextRef = useRef(null);
  const completionAudioRef = useRef(null);
  const errorAudioRef = useRef(null);

  // Initialize audio context and preload audio files
  const initializeAudio = useCallback(() => {
    try {
      // Create audio context for better browser compatibility
      if (!audioContextRef.current) {
        audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)();
      }

      // Preload audio files
      if (!completionAudioRef.current) {
        completionAudioRef.current = new Audio('/audio/completion.mp3');
        completionAudioRef.current.volume = 0.5;
        completionAudioRef.current.preload = 'auto';
      }

      if (!errorAudioRef.current) {
        errorAudioRef.current = new Audio('/audio/error.mp3');
        errorAudioRef.current.volume = 0.5;
        errorAudioRef.current.preload = 'auto';
      }
    } catch (e) {
      console.log('Audio initialization failed:', e);
    }
  }, []);

  const playCompletionSound = useCallback(() => {
    try {
      initializeAudio();

      if (completionAudioRef.current) {
        // Reset audio to beginning
        completionAudioRef.current.currentTime = 0;

        // Multiple strategies to ensure audio plays even in background
        const playPromise = completionAudioRef.current.play();

        if (playPromise !== undefined) {
          playPromise
            .then(() => {
              console.log('✅ Completion sound played successfully');
            })
            .catch(e => {
              console.log('Completion sound blocked by browser:', e);
              // Fallback: Try to resume audio context
              if (audioContextRef.current && audioContextRef.current.state === 'suspended') {
                audioContextRef.current.resume().then(() => {
                  completionAudioRef.current.play().catch(console.log);
                });
              }
            });
        }

        // Additional fallback: Browser notification with sound
        if ('Notification' in window && Notification.permission === 'granted') {
          new Notification('Upload Completed! ✅', {
            body: 'All platforms have finished uploading',
            icon: '/favicon.ico',
            silent: false,
            requireInteraction: false
          });
        }
      }
    } catch (e) {
      console.log('Completion audio error:', e);
    }
  }, [initializeAudio]);

  const playErrorSound = useCallback(() => {
    try {
      initializeAudio();

      if (errorAudioRef.current) {
        // Reset audio to beginning
        errorAudioRef.current.currentTime = 0;

        // Multiple strategies to ensure audio plays even in background
        const playPromise = errorAudioRef.current.play();

        if (playPromise !== undefined) {
          playPromise
            .then(() => {
              console.log('❌ Error sound played successfully');
            })
            .catch(e => {
              console.log('Error sound blocked by browser:', e);
              // Fallback: Try to resume audio context
              if (audioContextRef.current && audioContextRef.current.state === 'suspended') {
                audioContextRef.current.resume().then(() => {
                  errorAudioRef.current.play().catch(console.log);
                });
              }
            });
        }

        // Additional fallback: Browser notification with sound
        if ('Notification' in window && Notification.permission === 'granted') {
          new Notification('Upload Failed! ❌', {
            body: 'Some uploads encountered errors',
            icon: '/favicon.ico',
            silent: false,
            requireInteraction: false
          });
        }
      }
    } catch (e) {
      console.log('Error audio error:', e);
    }
  }, [initializeAudio]);

  // WebSocket connection management
  const connectWebSocket = useCallback(() => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      return;
    }

    const wsUrl = BACKEND_URL.replace('http', 'ws') + '/ws';
    const ws = new WebSocket(wsUrl);

    ws.onopen = () => {
      console.log('WebSocket connected');
      wsRef.current = ws;
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
        reconnectTimeoutRef.current = null;
      }
    };

    ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);

        if (data.type === 'initial_state') {
          setQueueStatus({
            total_queued: data.data.queue.length,
            total_in_progress: data.data.active.length,
            total_completed: data.data.completed.length,
            total_failed: data.data.failed.length,
            current_queue: data.data.queue,
            active_uploads: data.data.active,
            completed_uploads: data.data.completed
          });
        } else if (data.type === 'task_update') {
          // Handle real-time task updates
          const task = data.data;

          // Check if task just completed - play audio based on platform success/failure
          const completedPlatforms = Object.values(task.platform_status || {}).filter(status => status === 'completed').length;
          const failedPlatforms = Object.values(task.platform_status || {}).filter(status => status === 'failed').length;
          const totalPlatforms = PLATFORMS.length;

          if (task.status === 'completed') {
            if (completedPlatforms === totalPlatforms) {
              // All 5 platforms succeeded - play completion sound
              playCompletionSound();
              toast.success(`✅ Upload completed on all platforms: ${task.filename}`, {
                duration: 4000,
                style: {
                  background: '#065f46',
                  color: '#ffffff',
                  border: '1px solid #10b981'
                }
              });
            } else if (failedPlatforms > 0) {
              // Some platforms failed - play error sound
              playErrorSound();
              toast.error(`❌ Upload completed with failures: ${task.filename} (${completedPlatforms}/${totalPlatforms} platforms succeeded)`, {
                duration: 4000,
                style: {
                  background: '#7f1d1d',
                  color: '#ffffff',
                  border: '1px solid #ef4444'
                }
              });
            }
          } else if (task.status === 'failed') {
            playErrorSound();
            toast.error(`❌ Upload failed: ${task.filename}`, {
              duration: 4000,
              style: {
                background: '#7f1d1d',
                color: '#ffffff',
                border: '1px solid #ef4444'
              }
            });
          }
        } else if (data.type === 'csv_embed_ready') {
          // Handle embed codes becoming available
          const { filename, embed_codes_count, total_platforms } = data.data;
          console.log(`📊 CSV: ${filename} has ${embed_codes_count}/${total_platforms} embed codes ready`);

        } else if (data.type === 'csv_available') {
          // Handle CSV becoming available for download
          playCompletionSound();
          toast.success('📊 CSV file is ready for download with available embed codes!', {
            duration: 6000,
            style: {
              background: '#065f46',
              color: '#ffffff',
              border: '1px solid #10b981'
            },
            icon: '📊'
          });

        } else if (data.type === 'dmca_detected') {
          // Handle DMCA false positive detection
          const { filename, dmca_message, file_code } = data.data;
          playCompletionSound(); // Use completion sound as requested

          toast.error(`🚫 LuluStream DMCA False Positive Detected!\n\nFile: ${filename}\nCode: ${file_code}\nMessage: ${dmca_message}\n\nThis appears to be a false positive. The file has been uploaded but may not be playable.`, {
            duration: Infinity, // Requires manual dismissal
            style: {
              background: '#7f1d1d',
              color: '#ffffff',
              border: '2px solid #dc2626',
              maxWidth: '600px',
              whiteSpace: 'pre-line'
            },
            icon: '🚫'
          });

          console.log(`🚫 DMCA False Positive: ${filename} - ${dmca_message}`);
        }

        // Refresh queue status after any message
        fetch(`${BACKEND_URL}/api/queue/status`)
          .then(response => response.ok ? response.json() : null)
          .then(data => {
            if (data) {
              const prevCompleted = queueStatus.total_completed || 0;
              setQueueStatus(data);

              // Play completion sound when ALL uploads are finished
              if (data.total_completed > 0 &&
                  data.total_active === 0 &&
                  data.total_queued === 0 &&
                  data.total_completed > prevCompleted) {
                playCompletionSound();
                toast.success('🎉 All uploads completed! Ready to download CSV.', {
                  duration: 6000,
                  style: {
                    background: '#065f46',
                    color: '#ffffff',
                    border: '1px solid #10b981'
                  }
                });
              }
            }
          })
          .catch(console.error);

      } catch (e) {
        console.error('Error parsing WebSocket message:', e);
      }
    };

    ws.onclose = () => {
      console.log('WebSocket disconnected');
      wsRef.current = null;

      // Reconnect after 3 seconds
      reconnectTimeoutRef.current = setTimeout(() => {
        connectWebSocket();
      }, 3000);
    };

    ws.onerror = (error) => {
      console.error('WebSocket error:', error);
    };

  }, [playCompletionSound, playErrorSound, queueStatus.total_completed]);

  // Fetch queue status
  const fetchQueueStatus = useCallback(async () => {
    try {
      const response = await fetch(`${BACKEND_URL}/api/queue/status`);
      if (response.ok) {
        const data = await response.json();
        setQueueStatus(data);
      }
    } catch (error) {
      console.error('Error fetching queue status:', error);
    }
  }, []);

  // Fetch platform status
  const fetchPlatformStatus = useCallback(async () => {
    try {
      const response = await fetch(`${BACKEND_URL}/api/platforms/status`);
      if (response.ok) {
        const data = await response.json();
        setPlatformStatus(data);
      }
    } catch (error) {
      console.error('Error fetching platform status:', error);
    }
  }, []);

  // Fetch retry queue status
  const fetchRetryQueueStatus = useCallback(async () => {
    try {
      const response = await fetch(`${BACKEND_URL}/api/retry-queue/status`);
      if (response.ok) {
        const data = await response.json();
        setRetryQueueStatus(data);
      }
    } catch (error) {
      console.error('Error fetching retry queue status:', error);
    }
  }, []);

  // Request notification permission and initialize audio
  const requestPermissions = useCallback(async () => {
    try {
      // Request notification permission
      if ('Notification' in window && Notification.permission === 'default') {
        const permission = await Notification.requestPermission();
        console.log('Notification permission:', permission);
      }

      // Initialize audio on first user interaction
      initializeAudio();

      // Resume audio context if suspended
      if (audioContextRef.current && audioContextRef.current.state === 'suspended') {
        await audioContextRef.current.resume();
        console.log('Audio context resumed');
      }
    } catch (e) {
      console.log('Permission request failed:', e);
    }
  }, [initializeAudio]);

  // Initialize on component mount
  useEffect(() => {
    connectWebSocket();
    fetchQueueStatus();
    fetchPlatformStatus();
    fetchRetryQueueStatus();

    // Request permissions on app load
    requestPermissions();

    // Set up periodic refresh
    const interval = setInterval(() => {
      fetchQueueStatus();
      fetchRetryQueueStatus();
    }, 10000); // Refresh every 10 seconds

    return () => {
      clearInterval(interval);
      if (wsRef.current) {
        wsRef.current.close();
      }
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
    };
  }, [connectWebSocket, fetchQueueStatus, fetchPlatformStatus, fetchRetryQueueStatus, requestPermissions]);

  // Handle user interaction to enable audio
  const handleUserInteraction = useCallback(() => {
    requestPermissions();
  }, [requestPermissions]);

  // Handle URL submission with duplicate detection
  const handleSubmit = async (e) => {
    e.preventDefault();

    // Ensure audio is enabled on user interaction
    handleUserInteraction();

    if (!urls.trim()) {
      toast.error('Please enter at least one URL');
      return;
    }

    const urlList = urls.split('\n').filter(url => url.trim()).map(url => url.trim());

    if (urlList.length === 0) {
      toast.error('Please enter valid URLs');
      return;
    }

    if (urlList.length > 50) {
      toast.error('Maximum 50 URLs allowed at once');
      return;
    }

    setIsSubmitting(true);

    try {
      // STEP 1: Check for duplicates BEFORE submitting
      console.log('🔍 Checking for duplicates...');
      const duplicateCheckResponse = await fetch(`${BACKEND_URL}/api/upload/check-duplicates`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          urls: urlList,
          session_id: sessionId
        }),
      });

      if (duplicateCheckResponse.ok) {
        const duplicateData = await duplicateCheckResponse.json();

        // Check if any duplicates were found
        if (duplicateData.duplicates_found > 0) {
          playErrorSound();

          // Create detailed duplicate message
          const duplicateFiles = duplicateData.results.filter(r => r.has_duplicates);
          let duplicateMessage = `🚫 DUPLICATE FILES DETECTED!\n\n`;
          duplicateMessage += `Found ${duplicateData.duplicates_found} duplicate file(s):\n\n`;

          duplicateFiles.forEach((file, index) => {
            duplicateMessage += `${index + 1}. ${file.filename}\n`;
            duplicateMessage += `   Platforms: ${file.platforms_affected.join(', ')}\n`;
            duplicateMessage += `   Total duplicates: ${file.total_duplicates}\n\n`;
          });

          duplicateMessage += `❌ Upload blocked to prevent API rate limiting and duplicate files.\n`;
          duplicateMessage += `Please remove duplicate URLs and try again.`;

          // Show persistent error toast with custom close button
          toast.custom((t) => (
            <div
              className={`${
                t.visible ? 'animate-enter' : 'animate-leave'
              } max-w-md w-full bg-red-900 shadow-lg rounded-lg pointer-events-auto flex ring-1 ring-black ring-opacity-5`}
            >
              <div className="flex-1 w-0 p-4">
                <div className="flex items-start">
                  <div className="flex-shrink-0">
                    <span className="text-2xl">🚫</span>
                  </div>
                  <div className="ml-3 flex-1">
                    <p className="text-sm font-medium text-white">
                      DUPLICATE FILES DETECTED!
                    </p>
                    <p className="mt-1 text-sm text-red-100 whitespace-pre-line">
                      {duplicateMessage}
                    </p>
                  </div>
                </div>
              </div>
              <div className="flex border-l border-red-800">
                <button
                  onClick={() => toast.dismiss(t.id)}
                  className="w-full border border-transparent rounded-none rounded-r-lg p-4 flex items-center justify-center text-sm font-medium text-red-100 hover:text-white hover:bg-red-800 focus:outline-none focus:ring-2 focus:ring-red-500"
                >
                  ✕
                </button>
              </div>
            </div>
          ), {
            duration: Infinity,
            position: 'top-center',
          });

          console.log('🚫 Upload blocked due to duplicates:', duplicateFiles);
          setIsSubmitting(false);
          return;
        }

        // Check for resumable uploads
        if (duplicateData.resumable_found > 0) {
          const resumableFiles = duplicateData.results.filter(r => r.can_resume);
          let resumeMessage = `⚠️ RESUMABLE UPLOADS DETECTED!\n\n`;
          resumeMessage += `Found ${duplicateData.resumable_found} resumable upload(s):\n\n`;

          resumableFiles.forEach((file, index) => {
            resumeMessage += `${index + 1}. ${file.filename}\n`;
            resumeMessage += `   Reason: ${file.resume_info?.reason || 'Unknown'}\n\n`;
          });

          resumeMessage += `⚠️ These uploads will be resumed instead of creating new files.`;

          toast.warning(resumeMessage, {
            duration: 8000,
            style: {
              background: '#92400e',
              color: '#ffffff',
              border: '2px solid #f59e0b',
              maxWidth: '600px',
              whiteSpace: 'pre-line'
            },
            icon: '⚠️'
          });
        }
      }

      // STEP 2: If no duplicates, proceed with upload
      console.log('✅ No duplicates found, proceeding with upload...');
      const response = await fetch(`${BACKEND_URL}/api/upload/submit`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          urls: urlList,
          session_id: sessionId
        }),
      });

      if (response.ok) {
        const data = await response.json();
        setSessionId(data.session_id);
        setUrls(''); // Clear input

        let successMessage = `✅ Queued ${data.queued_count} URLs for upload`;
        if (data.duplicate_count > 0) {
          successMessage += ` (${data.duplicate_count} duplicates blocked)`;
        }

        toast.success(successMessage, {
          duration: 3000,
          style: {
            background: '#065f46',
            color: '#ffffff',
            border: '1px solid #10b981'
          }
        });

        // Refresh status
        fetchQueueStatus();
      } else {
        const error = await response.json();
        playErrorSound();
        toast.error(`❌ Error: ${error.detail}`);
      }
    } catch (error) {
      playErrorSound();
      toast.error(`❌ Network error: ${error.message}`);
    } finally {
      setIsSubmitting(false);
    }
  };



  // Cancel upload
  const handleCancelUpload = async (taskId) => {
    try {
      const response = await fetch(`${BACKEND_URL}/api/upload/cancel/${taskId}`, {
        method: 'DELETE'
      });

      if (response.ok) {
        toast.success('✅ Upload cancelled');
        fetchQueueStatus();
      } else {
        toast.error('❌ Failed to cancel upload');
      }
    } catch (error) {
      toast.error(`❌ Cancel error: ${error.message}`);
    }
  };

  // Manual verification for failed uploads (only re-uploads to failed platforms)
  const handleVerifyUpload = async (taskId) => {
    try {
      toast.loading('🔍 Verifying and re-uploading to failed platforms only...', { id: `verify-${taskId}` });

      const response = await fetch(`${BACKEND_URL}/api/uploads/verify/${taskId}`, {
        method: 'POST'
      });

      if (response.ok) {
        const result = await response.json();
        const completedPlatforms = result.completed_platforms;
        const totalPlatforms = result.total_platforms;
        const skippedPlatforms = result.skipped_platforms || 0;
        const reUploadedPlatforms = result.re_uploaded_platforms || 0;

        toast.dismiss(`verify-${taskId}`);

        let message = `✅ Verification completed!\n`;
        message += `• Total platforms: ${totalPlatforms}\n`;
        message += `• Successful: ${completedPlatforms}\n`;
        message += `• Skipped (already successful): ${skippedPlatforms}\n`;
        message += `• Re-uploaded: ${reUploadedPlatforms}`;

        if (completedPlatforms === totalPlatforms) {
          playCompletionSound();
          toast.success(message, {
            duration: 6000,
            style: {
              background: '#065f46',
              color: '#ffffff',
              border: '1px solid #10b981',
              whiteSpace: 'pre-line'
            }
          });
        } else if (completedPlatforms > 0) {
          playCompletionSound();
          toast.success(message, {
            duration: 6000,
            style: {
              background: '#92400e',
              color: '#ffffff',
              border: '1px solid #f59e0b',
              whiteSpace: 'pre-line'
            }
          });
        } else {
          playErrorSound();
          toast.error('❌ No successful uploads found during verification');
        }

        fetchQueueStatus();
      } else {
        const error = await response.json();
        toast.dismiss(`verify-${taskId}`);
        playErrorSound();
        toast.error(`❌ Verification failed: ${error.detail}`);
      }
    } catch (error) {
      toast.dismiss(`verify-${taskId}`);
      playErrorSound();
      toast.error(`❌ Verification error: ${error.message}`);
    }
  };

  // Clear completed uploads
  const handleClearCompleted = async () => {
    try {
      const response = await fetch(`${BACKEND_URL}/api/uploads/clear-completed`, {
        method: 'DELETE'
      });

      if (response.ok) {
        toast.success('✅ Completed uploads cleared');
        fetchQueueStatus();
      } else {
        toast.error('❌ Failed to clear completed uploads');
      }
    } catch (error) {
      toast.error(`❌ Clear error: ${error.message}`);
    }
  };

  // Clear retry queue
  const handleClearRetryQueue = async () => {
    try {
      const response = await fetch(`${BACKEND_URL}/api/retry-queue/clear`, {
        method: 'DELETE'
      });

      if (response.ok) {
        const result = await response.json();
        toast.success(`✅ ${result.message}`);
        fetchRetryQueueStatus();
        fetchQueueStatus();
      } else {
        toast.error('❌ Failed to clear retry queue');
      }
    } catch (error) {
      toast.error(`❌ Retry queue clear error: ${error.message}`);
    }
  };

  // Clear entire queue
  const handleClearQueue = async () => {
    try {
      const response = await fetch(`${BACKEND_URL}/api/queue/clear`, {
        method: 'DELETE'
      });

      if (response.ok) {
        toast.success('✅ Entire queue cleared');
        fetchQueueStatus();
      } else {
        toast.error('❌ Failed to clear queue');
      }
    } catch (error) {
      toast.error(`❌ Clear queue error: ${error.message}`);
    }
  };

  // End all tasks
  const handleEndAllTasks = async () => {
    try {
      const response = await fetch(`${BACKEND_URL}/api/uploads/end-all`, {
        method: 'POST'
      });

      if (response.ok) {
        const result = await response.json();
        toast.success(`✅ ${result.message}`);
        fetchQueueStatus();
      } else {
        toast.error('❌ Failed to end all tasks');
      }
    } catch (error) {
      toast.error(`❌ End tasks error: ${error.message}`);
    }
  };

  // Format time remaining
  const formatTimeRemaining = (seconds) => {
    if (!seconds || seconds <= 0) return '0s';

    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) return `${hours}h ${minutes}m`;
    if (minutes > 0) return `${minutes}m ${secs}s`;
    return `${secs}s`;
  };

  return (
    <div className="min-h-screen text-white" style={{backgroundColor: '#141414'}}>
      <Toaster position="top-right" />

      {/* TEST: Modern UI Active */}
      <div className="fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg z-50 font-bold">
        🎨 MODERN UI ACTIVE
      </div>

      {/* Modern Header */}
      <header className="border-b border-gray-700 sticky top-0 z-50" style={{backgroundColor: '#1e1e1e'}}>
        <div className="max-w-6xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 rounded-xl flex items-center justify-center shadow-lg border" style={{backgroundColor:'#1a1a1a', borderColor:'#22d3ee'}}>
                <Upload className="w-7 h-7" style={{color:'#7dd3fc'}} />
              </div>
              <div>
                <h1 className="text-2xl font-bold">
                  <span className="bg-clip-text text-transparent" style={{backgroundImage:'linear-gradient(90deg,#22d3ee,#60a5fa)'}}>
                    Video Upload Hub
                  </span>
                </h1>
                <p className="text-gray-400 text-sm">Multi-platform automation</p>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <div className="flex items-center space-x-2 px-3 py-1.5 rounded-full" style={{backgroundColor: '#242424'}}>
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <span className="text-green-400 text-sm font-medium">Online</span>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Tab Navigation */}
      <div className="max-w-6xl mx-auto px-4 pt-6">
        <div className="border-b border-gray-700">
          <nav className="flex space-x-8">
            <button
              onClick={() => setActiveTab('upload')}
              className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ${
                activeTab === 'upload'
                  ? 'border-blue-400 text-blue-400'
                  : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'
              }`}
            >
              <div className="flex items-center space-x-2">
                <Upload className="w-4 h-4" />
                <span>Upload Manager</span>
              </div>
            </button>
            <button
              onClick={() => setActiveTab('duplicates')}
              className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ${
                activeTab === 'duplicates'
                  ? 'border-purple-400 text-purple-400'
                  : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'
              }`}
            >
              <div className="flex items-center space-x-2">
                <FileX className="w-4 h-4" />
                <span>Duplicate File Checker</span>
              </div>
            </button>
            <button
              onClick={() => setActiveTab('embed-extract')}
              className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ${
                activeTab === 'embed-extract'
                  ? 'border-green-400 text-green-400'
                  : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'
              }`}
            >
              <div className="flex items-center space-x-2">
                <Code className="w-4 h-4" />
                <span>Embed Code Extract</span>
              </div>
            </button>
          </nav>
        </div>
      </div>

      <main className="max-w-6xl mx-auto px-4 py-6 space-y-8">
        {activeTab === 'upload' && (
          <>
            {/* Platform Status Grid */}
            <section>
              <div className="flex items-center space-x-3 mb-6">
                <Monitor className="w-6 h-6 text-blue-400" />
                <h2 className="text-xl font-semibold">Platform Status</h2>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4">
                {PLATFORMS.map((platform) => (
                  <div key={platform.name} className={`border border-gray-600 rounded-xl p-4 hover:border-gray-500 transition-all duration-200 ${platform.name}`} style={{backgroundColor: '#242424'}}>
                <div className="flex items-center justify-between mb-3">
                  <span
                    className="inline-flex items-center justify-center w-6 h-6 rounded-full"
                    style={{ backgroundColor: PLATFORM_COLOR_HEX[platform.color] }}
                  />
                  {platformStatus[platform.name]?.configured ? (
                    <CheckCircle className="w-5 h-5 text-green-400" />
                  ) : (
                    <XCircle className="w-5 h-5 text-red-400" />
                  )}
                </div>
                <h3 className="font-medium text-white mb-1">{platform.display}</h3>
                <p className={`text-sm ${platformStatus[platform.name]?.configured ? 'text-green-400' : 'text-red-400'}`}>
                  {platformStatus[platform.name]?.configured ? 'Ready' : 'Not configured'}
                </p>

                {/* Rate Limit Status */}
                {platformStatus[platform.name]?.rate_limit?.is_limited && (
                  <div className="mt-2 p-2 bg-yellow-900/30 border border-yellow-600 rounded">
                    <p className="text-xs text-yellow-400">
                      Rate Limited: {Math.ceil(platformStatus[platform.name].rate_limit.wait_time)}s
                    </p>
                  </div>
                )}

                {/* Retry Queue Count */}
                {retryQueueStatus[platform.name]?.retry_queue?.count > 0 && (
                  <div className="mt-2 p-2 bg-blue-900/30 border border-blue-600 rounded">
                    <p className="text-xs text-blue-400">
                      Retry Queue: {retryQueueStatus[platform.name].retry_queue.count} tasks
                    </p>
                  </div>
                )}
                  </div>
                ))}
              </div>
            </section>

            {/* Rate Limit & Retry Queue Status */}
            {Object.values(retryQueueStatus).some(platform => platform?.rate_limit?.is_limited || platform?.retry_queue?.count > 0) && (
              <section>
                <div className="flex items-center justify-between mb-6">
              <div className="flex items-center space-x-3">
                <Clock className="w-6 h-6 text-orange-400" />
                <h2 className="text-xl font-semibold">Rate Limits & Retry Queue</h2>
              </div>

              {/* Clear Retry Queue Button */}
              {Object.values(retryQueueStatus).some(platform => platform?.retry_queue?.count > 0) && (
                <button
                  onClick={handleClearRetryQueue}
                  className="flex items-center px-4 py-2 bg-gradient-to-r from-red-600 to-red-700 hover:from-red-500 hover:to-red-600 text-white font-medium rounded-lg transition-all duration-200"
                >
                  <Trash2 className="w-4 h-4 mr-2" />
                  Clear Retry Queue
                </button>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {PLATFORMS.map((platform) => {
                const platformRetryStatus = retryQueueStatus[platform.name];
                const isRateLimited = platformRetryStatus?.rate_limit?.is_limited;
                const retryQueueCount = platformRetryStatus?.retry_queue?.count || 0;

                if (!isRateLimited && retryQueueCount === 0) return null;

                return (
                  <div key={platform.name} className="border border-gray-600 rounded-xl p-4" style={{backgroundColor: '#242424'}}>
                    <div className="flex items-center space-x-3 mb-3">
                      <span
                        className="inline-flex items-center justify-center w-6 h-6 rounded-full"
                        style={{ backgroundColor: PLATFORM_COLOR_HEX[platform.color] }}
                      />
                      <h3 className="font-medium text-white">{platform.display}</h3>
                    </div>

                    {isRateLimited && (
                      <div className="mb-3 p-3 bg-yellow-900/30 border border-yellow-600 rounded">
                        <div className="flex items-center space-x-2 mb-1">
                          <Clock className="w-4 h-4 text-yellow-400" />
                          <span className="text-sm font-medium text-yellow-400">Rate Limited</span>
                        </div>
                        <p className="text-xs text-yellow-300">
                          Retry in: {Math.ceil(platformRetryStatus.rate_limit.wait_time)}s
                        </p>
                        <p className="text-xs text-gray-400 mt-1">
                          {platformRetryStatus.rate_limit.reason}
                        </p>
                      </div>
                    )}

                    {retryQueueCount > 0 && (
                      <div className="p-3 bg-blue-900/30 border border-blue-600 rounded">
                        <div className="flex items-center space-x-2 mb-1">
                          <Upload className="w-4 h-4 text-blue-400" />
                          <span className="text-sm font-medium text-blue-400">Retry Queue</span>
                        </div>
                        <p className="text-xs text-blue-300">
                          {retryQueueCount} task{retryQueueCount !== 1 ? 's' : ''} waiting
                        </p>
                        {platformRetryStatus.retry_queue.tasks.slice(0, 3).map((task, index) => (
                          <p key={index} className="text-xs text-gray-400 mt-1 truncate">
                            • {task.filename}
                          </p>
                        ))}
                        {retryQueueCount > 3 && (
                          <p className="text-xs text-gray-500 mt-1">
                            +{retryQueueCount - 3} more...
                          </p>
                        )}
                      </div>
                    )}
                  </div>
                );
              })}
                </div>
              </section>
            )}

            {/* URL Input Section */}
            <section>
              <div className="flex items-center space-x-3 mb-6">
                <Plus className="w-6 h-6 text-green-400" />
                <h2 className="text-xl font-semibold">Add Remote URLs</h2>
              </div>

          <div className="border border-gray-600 rounded-xl p-6" style={{backgroundColor: '#242424'}}>
            <form onSubmit={handleSubmit} className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-3">
                  Remote URLs (one per line, maximum 50)
                </label>
                <textarea
                  value={urls}
                  onChange={(e) => setUrls(e.target.value)}
                  placeholder="https://example.com/video1.mp4&#10;https://example.com/video2.mp4&#10;https://example.com/video3.mp4"
                  className="w-full px-4 py-3 border border-gray-600 rounded-lg text-white placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors resize-none"
                  style={{
                    backgroundColor: '#1a1a1a',
                    height: '138px', // 15% increase from 120px (h-40 with 25% reduction)
                    fontSize: '0.525rem', // 30% reduction from 0.75rem (base font size with 25% reduction)
                    lineHeight: '0.7875rem' // Proportional line height reduction
                  }}
                  disabled={isSubmitting}
                />
                <div className="flex justify-between items-center mt-3 text-sm">
                  <span className="text-gray-400">
                    {urls.split('\n').filter(url => url.trim()).length} URLs entered
                  </span>
                  <div className="flex space-x-4 text-gray-400">
                    <span>Queued: <span className="text-yellow-400 font-medium">{queueStatus.total_queued}</span></span>
                    <span>Active: <span className="text-blue-400 font-medium">{queueStatus.total_in_progress}</span></span>
                  </div>
                </div>
              </div>

              <div className="flex flex-wrap gap-3">
                <button
                  type="submit"
                  disabled={isSubmitting || !urls.trim()}
                  className="flex items-center px-6 py-3 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-500 hover:to-blue-600 disabled:from-gray-700 disabled:to-gray-700 disabled:cursor-not-allowed text-white font-medium rounded-lg transition-all duration-200 shadow-lg"
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                      Queueing...
                    </>
                  ) : (
                    <>
                      <Upload className="w-5 h-5 mr-2" />
                      Queue for Upload
                    </>
                  )}
                </button>

                <button
                  type="button"
                  onClick={() => setUrls('')}
                  disabled={!urls.trim()}
                  className="flex items-center px-4 py-3 bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-500 hover:to-gray-600 disabled:from-gray-700 disabled:to-gray-700 disabled:cursor-not-allowed text-white font-medium rounded-lg transition-all duration-200"
                >
                  <Trash2 className="w-5 h-5 mr-2" />
                  Clear URLs
                </button>

                <button
                  type="button"
                  onClick={handleClearCompleted}
                  disabled={queueStatus.total_completed === 0}
                  className="flex items-center px-4 py-3 bg-gradient-to-r from-yellow-600 to-yellow-700 hover:from-yellow-500 hover:to-yellow-600 disabled:from-gray-700 disabled:to-gray-700 disabled:cursor-not-allowed text-white font-medium rounded-lg transition-all duration-200"
                >
                  <Trash2 className="w-5 h-5 mr-2" />
                  Clear Completed
                </button>

                <button
                  type="button"
                  onClick={handleClearQueue}
                  disabled={queueStatus.total_queued === 0 && queueStatus.total_in_progress === 0}
                  className="flex items-center px-4 py-3 bg-gradient-to-r from-orange-600 to-orange-700 hover:from-orange-500 hover:to-orange-600 disabled:from-gray-700 disabled:to-gray-700 disabled:cursor-not-allowed text-white font-medium rounded-lg transition-all duration-200"
                >
                  <Trash2 className="w-5 h-5 mr-2" />
                  Clear Queue
                </button>

                <button
                  type="button"
                  onClick={handleEndAllTasks}
                  disabled={queueStatus.total_in_progress === 0 && queueStatus.total_queued === 0}
                  className="flex items-center px-4 py-3 bg-gradient-to-r from-red-600 to-red-700 hover:from-red-500 hover:to-red-600 disabled:from-gray-700 disabled:to-gray-700 disabled:cursor-not-allowed text-white font-medium rounded-lg transition-all duration-200"
                >
                  <XCircle className="w-5 h-5 mr-2" />
                  End All Tasks
                </button>
              </div>

              <div className="flex justify-between items-center pt-4 border-t border-gray-700 text-gray-400" style={{fontSize: '0.9375rem'}}>
                <span>Total Completed: <span className="text-green-400 font-medium">{queueStatus.total_completed}</span></span>
                <span>Failed: <span className="text-red-400 font-medium">{queueStatus.total_failed}</span></span>
              </div>
            </form>
              </div>
            </section>

            {/* Queue Statistics */}
            <section>
          <div className="flex items-center space-x-3 mb-6">
            <BarChart3 className="w-6 h-6 text-yellow-400" />
            <h2 className="text-xl font-semibold">Queue Statistics</h2>
          </div>

          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="border border-yellow-600 rounded-xl p-6" style={{backgroundColor: '#242424'}}>
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-yellow-400 text-sm font-medium">Queued</p>
                  <p className="text-3xl font-bold text-yellow-300">{queueStatus.total_queued}</p>
                </div>
                <Clock className="w-8 h-8 text-yellow-400" />
              </div>
            </div>

            <div className="border border-blue-600 rounded-xl p-6" style={{backgroundColor: '#242424'}}>
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-blue-400 text-sm font-medium">In Progress</p>
                  <p className="text-3xl font-bold text-blue-300">{queueStatus.total_in_progress}</p>
                </div>
                <Zap className="w-8 h-8 text-blue-400" />
              </div>
            </div>

            <div className="border border-green-600 rounded-xl p-6" style={{backgroundColor: '#242424'}}>
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-green-400 text-sm font-medium">Completed</p>
                  <p className="text-3xl font-bold text-green-300">{queueStatus.total_completed}</p>
                </div>
                <CheckCircle className="w-8 h-8 text-green-400" />
              </div>
            </div>

            <div className="border border-red-600 rounded-xl p-6" style={{backgroundColor: '#242424'}}>
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-red-400 text-sm font-medium">Failed</p>
                  <p className="text-3xl font-bold text-red-300">{queueStatus.total_failed}</p>
                </div>
                <XCircle className="w-8 h-8 text-red-400" />
              </div>
              </div>
            </div>
            </section>

            {/* Active Uploads */}
            {queueStatus.active_uploads.length > 0 && (
              <section>
                <div className="flex items-center space-x-3 mb-6">
              <Zap className="w-6 h-6 text-blue-400" />
              <h2 className="text-xl font-semibold">Active Uploads</h2>
              <span className="bg-blue-600 text-white text-sm px-2 py-1 rounded-full">
                {queueStatus.active_uploads.length}
              </span>
            </div>

            <div className="space-y-6">
              {queueStatus.active_uploads.map((task) => (
                <div key={task.id} className="border border-gray-700 rounded-xl p-6 hover:border-gray-600 transition-all duration-200" style={{backgroundColor: '#242424'}}>
                  <div className="flex items-start justify-between mb-6">
                    <div className="flex-1 min-w-0">
                      <h3 className="text-lg font-semibold text-white mb-2 truncate">{task.filename}</h3>
                      <p className="text-gray-400 truncate" style={{fontSize: '0.9375rem'}}>{task.url}</p>
                      <div className="flex items-center space-x-4 mt-2 text-gray-300">
                        <span className="text-sm text-blue-400 capitalize bg-blue-900/30 px-2 py-1 rounded">
                          {task.status.replace('_', ' ')}
                        </span>
                      </div>
                    </div>
                    <button
                      onClick={() => handleCancelUpload(task.id)}
                      className="p-2 text-red-400 hover:text-red-300 hover:bg-red-900/30 rounded-lg transition-colors"
                      title="Cancel Upload"
                    >
                      <XCircle className="w-5 h-5" />
                    </button>
                  </div>

                  {/* Platform Progress Grid */}
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {PLATFORMS.map((platform) => {
                      const progress = task.progress?.[platform.name] || 0;
                      const speed = task.speeds?.[platform.name] || 0;
                      const eta = task.eta_seconds?.[platform.name] || 0;
                      const status = task.platform_status?.[platform.name] || 'queued';
                      const hasEmbedCode = task.embed_codes?.[platform.name];

                      return (
                        <div key={platform.name} className="border border-gray-600 rounded-lg p-4" style={{backgroundColor: '#1a1a1a'}}>
                          <div className="flex items-center justify-between mb-3">
                            <div className="flex items-center space-x-2">
                              <span
                                className="inline-flex items-center justify-center w-4 h-4 rounded-full"
                                style={{ backgroundColor: PLATFORM_COLOR_HEX[platform.color] }}
                              />
                              <span className="font-medium text-white">{platform.display}</span>
                            </div>
                            {hasEmbedCode && (
                              <CheckCircle className="w-5 h-5 text-green-400" />
                            )}
                          </div>

                          <div className="space-y-2">
                            <div className="flex justify-between text-sm">
                              <span className={`status-${status.replace('_', '-')} capitalize`}>
                                {status.replace('_', ' ')}
                              </span>
                              <span className="text-white font-medium">{progress.toFixed(1)}%</span>
                            </div>

                            <div className="w-full rounded-full h-2" style={{backgroundColor: '#2a2a2a'}}>
                              <div
                                className={`h-full transition-all duration-300 rounded-full progress-${platform.name}`}
                                style={{ width: `${Math.max(progress, 0)}%` }}
                              />
                            </div>

                            <div className="flex justify-between text-gray-400" style={{fontSize: '0.8125rem'}}>
                              {speed > 0 && (
                                <span>{speed.toFixed(1)} MB/s</span>
                              )}
                              {eta > 0 && (
                                <span>ETA: {formatTimeRemaining(eta)}</span>
                              )}
                              {task.error_messages?.[platform.name] && (
                                <span className="text-red-400 flex items-center">
                                  <AlertTriangle className="w-3 h-3 mr-1" />
                                  Error
                                </span>
                              )}
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              ))}
              </div>
              </section>
            )}

            {/* Queue Section */}
            {queueStatus.current_queue.length > 0 && (
              <section>
            <div className="flex items-center space-x-3 mb-6">
              <Clock className="w-6 h-6 text-yellow-400" />
              <h2 className="text-xl font-semibold">Upload Queue</h2>
              <span className="bg-yellow-600 text-white text-sm px-2 py-1 rounded-full">
                {queueStatus.current_queue.length}
              </span>
            </div>

            <div className="border border-gray-700 rounded-xl p-6" style={{backgroundColor: '#242424'}}>
              <div className="space-y-3">
                {queueStatus.current_queue.slice(0, 10).map((task, index) => (
                  <div key={task.id} className="flex items-center justify-between p-4 border border-gray-600 rounded-lg" style={{backgroundColor: '#1a1a1a'}}>
                    <div className="flex items-center space-x-4 flex-1 min-w-0">
                      <span className="text-sm text-yellow-400 font-medium bg-yellow-900/30 px-2 py-1 rounded">
                        #{index + 1}
                      </span>
                      <div className="min-w-0 flex-1">
                        <p className="font-medium text-white truncate">{task.filename}</p>
                        <p className="text-gray-400 truncate" style={{fontSize: '0.9375rem'}}>{task.url}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-yellow-400 bg-yellow-900/30 px-2 py-1 rounded">Waiting</span>
                      <Clock className="w-4 h-4 text-yellow-400" />
                    </div>
                  </div>
                ))}
                {queueStatus.current_queue.length > 10 && (
                  <div className="text-center py-4 text-gray-400 text-sm border-t border-gray-700">
                    ... and {queueStatus.current_queue.length - 10} more items in queue
                  </div>
                )}
              </div>
              </div>
              </section>
            )}

            {/* Completed Uploads */}
            {queueStatus.completed_uploads.length > 0 && (
              <section>
            <div className="flex items-center space-x-3 mb-6">
              <CheckCircle className="w-6 h-6 text-green-400" />
              <h2 className="text-xl font-semibold">Recent Completed</h2>
              <span className="bg-green-600 text-white text-sm px-2 py-1 rounded-full">
                {queueStatus.completed_uploads.length}
              </span>
            </div>

            <div className="space-y-4">
              {queueStatus.completed_uploads.slice(-5).reverse().map((task) => (
                <div key={task.id} className="border border-green-700/50 rounded-xl p-6" style={{backgroundColor: '#242424'}}>
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex-1 min-w-0">
                      <h3 className="text-lg font-semibold text-white flex items-center space-x-2 mb-2">
                        <CheckCircle className="w-5 h-5 text-green-400" />
                        <span className="truncate">{task.filename}</span>
                      </h3>
                      <p className="text-gray-400 truncate" style={{fontSize: '0.9375rem'}}>{task.url}</p>
                    </div>
                    <div className="flex items-center space-x-3">
                      {/* Show verification button if any platform failed */}
                      {PLATFORMS.some(platform => !task.embed_codes?.[platform.name]) && (
                        <button
                          onClick={() => handleVerifyUpload(task.id)}
                          className="flex items-center px-3 py-1 bg-yellow-600 hover:bg-yellow-500 text-white text-sm rounded-lg transition-colors"
                          title="Re-verify failed platforms"
                        >
                          <AlertTriangle className="w-4 h-4 mr-1" />
                          Verify
                        </button>
                      )}
                      <div className="text-sm text-green-400 bg-green-900/30 px-3 py-1 rounded">
                        {task.completed_at && new Date(task.completed_at).toLocaleTimeString()}
                      </div>
                    </div>
                  </div>

                  {/* Platform Status Grid */}
                  <div className="grid grid-cols-2 md:grid-cols-5 gap-3">
                    {PLATFORMS.map((platform) => {
                      const hasEmbedCode = task.embed_codes?.[platform.name];

                      return (
                        <div key={platform.name} className="border border-gray-600 rounded-lg p-3 text-center" style={{backgroundColor: '#1a1a1a'}}>
                          <div className="flex items-center justify-center space-x-2 mb-2">
                            <span
                              className="inline-flex items-center justify-center w-3.5 h-3.5 rounded-full"
                              style={{ backgroundColor: PLATFORM_COLOR_HEX[platform.color] }}
                            />
                            {hasEmbedCode ? (
                              <CheckCircle className="w-4 h-4 text-green-400" />
                            ) : (
                              <XCircle className="w-4 h-4 text-red-400" />
                            )}
                          </div>
                          <div className="text-sm font-medium text-white">{platform.display}</div>
                          <div className={`text-xs mt-1 ${hasEmbedCode ? 'text-green-400' : 'text-red-400'}`}>
                            {hasEmbedCode ? 'Success' : 'Failed'}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              ))}
              </div>
              </section>
            )}
          </>
        )}

        {activeTab === 'duplicates' && (
          <DuplicateFileChecker
            duplicateCheckerState={duplicateCheckerState}
            setDuplicateCheckerState={setDuplicateCheckerState}
            platforms={PLATFORMS}
            platformColorHex={PLATFORM_COLOR_HEX}
          />
        )}

        {activeTab === 'embed-extract' && (
          <EmbedCodeExtract
            embedExtractState={embedExtractState}
            setEmbedExtractState={setEmbedExtractState}
            platforms={PLATFORMS}
            platformColorHex={PLATFORM_COLOR_HEX}
          />
        )}
      </main>

      {/* Footer */}
      <footer className="border-t border-gray-700 mt-12" style={{backgroundColor: '#242424'}}>
        <div className="max-w-6xl mx-auto px-4 py-8 text-center">
          <div className="flex items-center justify-center space-x-2 mb-2">
            <Upload className="w-5 h-5 text-blue-400" />
            <span className="text-lg font-semibold text-white">Video Upload Automation Hub</span>
          </div>
          <p className="text-gray-400 text-sm">
            Multi-platform video upload automation for Lulustream, StreamP2P, RPMShare, FileMoon, and UpnShare
          </p>
        </div>
      </footer>
    </div>
  );
}

export default App;